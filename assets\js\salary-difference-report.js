// Professional Report Generator for Salary Difference
function printResults() {
    console.log('Print button clicked');
    
    if (!validateFormForReport()) {
        console.log('Form validation failed');
        return;
    }
    
    try {
        // Check if generateCompactProfessionalReport exists
        if (typeof generateCompactProfessionalReport !== 'function') {
            console.error('generateCompactProfessionalReport function not found');
            alert('خطأ: دالة إنشاء التقرير غير موجودة');
            return;
        }
        
        const printWindow = window.open('', '_blank', 'width=210mm,height=297mm');
        if (!printWindow) {
            alert('يرجى السماح للنوافذ المنبثقة لطباعة التقرير');
            return;
        }
        
        const reportHTML = generateCompactProfessionalReport();
        
        printWindow.document.write(reportHTML);
        printWindow.document.close();
        
        printWindow.onload = function() {
            printWindow.focus();
            setTimeout(() => {
                printWindow.print();
                printWindow.close();
            }, 500);
        };
        
        console.log('Print window opened successfully');
    } catch (error) {
        console.error('Print error:', error);
        alert('حدث خطأ في الطباعة: ' + error.message);
    }
}

function validateFormForReport() {
    // Check if we have results to print
    const resultsCard = document.getElementById('resultsCard');
    if (!resultsCard || resultsCard.style.display === 'none') {
        alert('يرجى إجراء الحساب أولاً قبل الطباعة');
        return false;
    }
    
    // Check if we have any content
    const resultsContent = document.getElementById('resultsContent');
    if (!resultsContent || !resultsContent.textContent.trim()) {
        alert('لا توجد نتائج للطباعة');
        return false;
    }
    
    return true;
}

function exportToPDF() {
    console.log('PDF export button clicked');
    
    if (!validateFormForReport()) {
        console.log('Form validation failed for PDF');
        return;
    }
    
    if (typeof jsPDF === 'undefined') {
        alert('مكتبة PDF غير متوفرة');
        return;
    }
    
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التصدير...';
    btn.disabled = true;
    
    try {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = generateCompactProfessionalReport();
        tempDiv.style.position = 'absolute';
        tempDiv.style.left = '-9999px';
        tempDiv.style.width = '210mm';
        document.body.appendChild(tempDiv);
        
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF('p', 'mm', 'a4');
        
        doc.html(tempDiv, {
            callback: function (doc) {
                const fileName = `تقرير-فرق-الراتب-${new Date().toISOString().split('T')[0]}.pdf`;
                doc.save(fileName);
                document.body.removeChild(tempDiv);
                
                // Reset button
                btn.innerHTML = originalText;
                btn.disabled = false;
                
                console.log('PDF exported successfully');
            },
            x: 5,
            y: 5,
            width: 200,
            windowWidth: 800,
            html2canvas: { scale: 0.75 }
        });
    } catch (error) {
        console.error('PDF Error:', error);
        alert('حدث خطأ في تصدير PDF: ' + error.message);
        btn.innerHTML = originalText;
        btn.disabled = false;
    }
}

// Helper function to format dates in Gregorian format
function formatDateGregorian(date) {
    if (!date || !(date instanceof Date)) return 'غير محدد';
    
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    
    return `${day}/${month}/${year}`;
}

// Generate compact professional report
function generateCompactProfessionalReport() {
    console.log('Generating compact professional report');
    
    // Get results data
    const resultsContent = document.getElementById('resultsContent');
    const periodDetails = document.getElementById('periodDetailsContent');
    
    if (!resultsContent) {
        throw new Error('لا توجد نتائج للطباعة');
    }
    
    // Get form data
    const startDate = document.getElementById('startDate')?.value || '';
    const endDate = document.getElementById('endDate')?.value || '';
    
    // Format dates
    const startDateFormatted = startDate ? formatDateGregorian(new Date(startDate)) : 'غير محدد';
    const endDateFormatted = endDate ? formatDateGregorian(new Date(endDate)) : 'غير محدد';
    
    // Get current date for print
    const printDate = new Date().toLocaleDateString('ar-SA');
    
    // Generate HTML report
    const reportHTML = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تقرير فرق الراتب</title>
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 12px;
                line-height: 1.4;
                color: #333;
                background: white;
                direction: rtl;
            }
            
            .report-container {
                max-width: 210mm;
                margin: 0 auto;
                padding: 15mm;
                background: white;
            }
            
            .report-header {
                text-align: center;
                border-bottom: 2px solid #2c5530;
                padding-bottom: 15px;
                margin-bottom: 20px;
            }
            
            .ministry-title {
                font-size: 16px;
                font-weight: bold;
                color: #2c5530;
                margin-bottom: 5px;
            }
            
            .department-title {
                font-size: 14px;
                color: #666;
                margin-bottom: 10px;
            }
            
            .report-title {
                font-size: 18px;
                font-weight: bold;
                color: #2c5530;
                margin-top: 10px;
            }
            
            .report-info {
                background: #f8f9fa;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
                border: 1px solid #dee2e6;
            }
            
            .info-row {
                display: flex;
                justify-content: space-between;
                margin-bottom: 8px;
            }
            
            .info-label {
                font-weight: bold;
                color: #495057;
            }
            
            .info-value {
                color: #212529;
            }
            
            .results-section {
                margin-bottom: 20px;
            }
            
            .section-title {
                font-size: 14px;
                font-weight: bold;
                color: #2c5530;
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 1px solid #dee2e6;
            }
            
            .result-item {
                display: flex;
                justify-content: space-between;
                padding: 8px 0;
                border-bottom: 1px solid #f1f3f4;
            }
            
            .result-label {
                font-weight: 500;
                color: #495057;
            }
            
            .result-value {
                font-weight: bold;
                color: #212529;
            }
            
            .total-result {
                background: #e8f5e8;
                padding: 12px;
                border-radius: 5px;
                margin-top: 10px;
                border: 1px solid #28a745;
            }
            
            .total-result .result-label,
            .total-result .result-value {
                color: #155724;
                font-size: 14px;
                font-weight: bold;
            }
            
            .difference-breakdown {
                background: #fff;
                border: 1px solid #dee2e6;
                border-radius: 5px;
                padding: 10px;
                margin-bottom: 10px;
            }
            
            .breakdown-year {
                font-weight: bold;
                color: #2c5530;
                margin-bottom: 5px;
            }
            
            .breakdown-item {
                display: flex;
                justify-content: space-between;
                padding: 3px 0;
                font-size: 11px;
            }
            
            .report-footer {
                margin-top: 30px;
                padding-top: 15px;
                border-top: 1px solid #dee2e6;
                text-align: center;
                font-size: 10px;
                color: #666;
            }
            
            @media print {
                body {
                    font-size: 11px;
                }
                
                .report-container {
                    padding: 10mm;
                }
                
                .report-header {
                    padding-bottom: 10px;
                    margin-bottom: 15px;
                }
                
                .report-info {
                    padding: 10px;
                    margin-bottom: 15px;
                }
                
                .results-section {
                    margin-bottom: 15px;
                }
                
                .total-result {
                    padding: 8px;
                }
            }
        </style>
    </head>
    <body>
        <div class="report-container">
            <!-- Header -->
            <div class="report-header">
                <div class="ministry-title">وزارة الدفاع - القوات البرية الملكية السعودية</div>
                <div class="department-title">فرع الشؤون الإدارية والمالية للقوات البرية بالجنوبية - معهد سلاح المشاة</div>
                <div class="report-title">تقرير حساب فرق الراتب</div>
            </div>
            
            <!-- Report Info -->
            <div class="report-info">شاش
                <div class="info-row">
                    <span class="info-label">تاريخ البداية:</span>
                    <span class="info-value">${startDateFormatted}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">تاريخ النهاية:</span>
                    <span class="info-value">${endDateFormatted}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">تاريخ الطباعة:</span>
                    <span class="info-value">${printDate}</span>
                </div>
            </div>
            
            <!-- Results -->
            <div class="results-section">
                <div class="section-title">نتائج الحساب</div>
                ${resultsContent.innerHTML}
            </div>
            
            <!-- Period Details -->
            ${periodDetails ? `
            <div class="results-section">
                <div class="section-title">تفاصيل الفترة</div>
                ${periodDetails.innerHTML}
            </div>
            ` : ''}
            
            <!-- Footer -->
            <div class="report-footer">
                <div>نظام حساب البدلات - فرع الشؤون الإدارية والمالية للقوات البرية بالجنوبية - معهد سلاح المشاة</div>
                <div>تاريخ الطباعة: ${printDate}</div>
            </div>
        </div>
    </body>
    </html>
    `;
    
    console.log('Report HTML generated successfully');
    return reportHTML;
}

// Debug function to check if everything is loaded
function debugPrintFunction() {
    console.log('=== DEBUG PRINT FUNCTION ===');
    console.log('printResults function exists:', typeof printResults);
    console.log('exportToPDF function exists:', typeof exportToPDF);
    console.log('generateCompactProfessionalReport function exists:', typeof generateCompactProfessionalReport);
    
    const resultsCard = document.getElementById('resultsCard');
    console.log('Results card exists:', !!resultsCard);
    console.log('Results card visible:', resultsCard ? resultsCard.style.display !== 'none' : false);
    
    const resultsContent = document.getElementById('resultsContent');
    console.log('Results content exists:', !!resultsContent);
    console.log('Results content has text:', resultsContent ? !!resultsContent.textContent.trim() : false);
}

// Call debug function when page loads
window.addEventListener('load', function() {
    console.log('Salary difference report page loaded');
    debugPrintFunction();
});

// Simple fallback print function
function printResultsSimple() {
    console.log('Using simple print function');
    
    if (!validateFormForReport()) {
        return;
    }
    
    try {
        // Hide non-printable elements
        const elementsToHide = document.querySelectorAll('.btn, .card-footer, nav, .sidebar');
        elementsToHide.forEach(el => {
            if (el) el.style.display = 'none';
        });
        
        // Print the page
        window.print();
        
        // Restore hidden elements
        setTimeout(() => {
            elementsToHide.forEach(el => {
                if (el) el.style.display = '';
            });
        }, 1000);
        
    } catch (error) {
        console.error('Simple print error:', error);
        alert('حدث خطأ في الطباعة');
    }
}

// Backup print function in case the main one doesn't work
window.printSalaryDifferenceReport = function() {
    console.log('Backup print function called');
    
    // Check if we have results
    const resultsCard = document.getElementById('resultsCard');
    if (!resultsCard || resultsCard.style.display === 'none') {
        alert('يرجى إجراء الحساب أولاً قبل الطباعة');
        return;
    }
    
    try {
        // Try the main function first
        if (typeof generateCompactProfessionalReport === 'function') {
            const reportHTML = generateCompactProfessionalReport();
            
            const printWindow = window.open('', '_blank');
            if (!printWindow) {
                // Fallback to simple print
                printResultsSimple();
                return;
            }
            
            printWindow.document.write(reportHTML);
            printWindow.document.close();
            
            setTimeout(() => {
                printWindow.focus();
                printWindow.print();
                printWindow.close();
            }, 1000);
        } else {
            // Use simple print as fallback
            printResultsSimple();
        }
        
    } catch (error) {
        console.error('Print error:', error);
        // Final fallback
        printResultsSimple();
    }
};

// Override the original printResults if it doesn't exist
if (typeof window.printResults === 'undefined') {
    window.printResults = window.printSalaryDifferenceReport;
    console.log('printResults function created');
}

// Also create exportToPDF if it doesn't exist
if (typeof window.exportToPDF === 'undefined') {
    window.exportToPDF = function() {
        console.log('Backup PDF export called');
        
        if (!validateFormForReport()) return;
        
        if (typeof jsPDF === 'undefined') {
            alert('مكتبة PDF غير متوفرة - سيتم استخدام الطباعة العادية');
            window.printResults();
            return;
        }
        
        // Use the existing PDF export logic
        const btn = event?.target;
        if (btn) {
            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التصدير...';
            btn.disabled = true;
        }
        
        try {
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = generateCompactProfessionalReport();
            tempDiv.style.position = 'absolute';
            tempDiv.style.left = '-9999px';
            tempDiv.style.width = '210mm';
            document.body.appendChild(tempDiv);
            
            const { jsPDF } = window.jspdf;
            const doc = new jsPDF('p', 'mm', 'a4');
            
            doc.html(tempDiv, {
                callback: function (doc) {
                    const fileName = `تقرير-فرق-الراتب-${new Date().toISOString().split('T')[0]}.pdf`;
                    doc.save(fileName);
                    document.body.removeChild(tempDiv);
                    
                    if (btn) {
                        btn.innerHTML = originalText;
                        btn.disabled = false;
                    }
                },
                x: 5,
                y: 5,
                width: 200,
                windowWidth: 800,
                html2canvas: { scale: 0.75 }
            });
        } catch (error) {
            console.error('PDF Error:', error);
            alert('حدث خطأ في تصدير PDF');
            if (btn) {
                btn.innerHTML = originalText;
                btn.disabled = false;
            }
        }
    };
    console.log('exportToPDF function created');
}

// Test function to manually trigger print
window.testPrint = function() {
    console.log('Manual test print triggered');
    window.printResults();
};

// Add event listeners as backup
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded - Setting up print functions');
    
    // Find print buttons and add event listeners
    const printButtons = document.querySelectorAll('button[onclick*="printResults"], .btn:contains("طباعة")');
    printButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Print button clicked via event listener');
            window.printResults();
        });
    });
    
    const pdfButtons = document.querySelectorAll('button[onclick*="exportToPDF"], .btn:contains("تصدير PDF")');
    pdfButtons.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('PDF button clicked via event listener');
            window.exportToPDF();
        });
    });
});




