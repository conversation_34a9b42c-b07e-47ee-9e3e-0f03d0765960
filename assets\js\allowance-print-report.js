function generatePrintableAllowanceReport() {
    console.log('Starting print report generation...');
    
    // Get calculation data
    const startDateInput = document.getElementById('startDate');
    const endDateInput = document.getElementById('endDate');
    const currentSalaryInput = document.getElementById('currentSalary');

    if (!startDateInput || !endDateInput) {
        alert('لا توجد بيانات كافية لعرض التقرير');
        return;
    }

    if (!startDateInput.value || !endDateInput.value) {
        alert('يرجى ملء التواريخ المطلوبة');
        return;
    }

    // Only check current salary if not fixed type
    if (currentAllowance && currentAllowance.type !== 'fixed') {
        if (!currentSalaryInput || !currentSalaryInput.value) {
            alert('يرجى ملء جميع الحقول المطلوبة');
            return;
        }
    }

    if (!currentAllowance) {
        alert('لم يتم تحديد نوع البدل');
        return;
    }

    // Perform calculation to get detailed breakdown
    let calculationResult;
    try {
        console.log('Performing calculation...');
        calculationResult = performCalculation();
        console.log('Calculation result:', calculationResult);
    } catch (error) {
        console.error('Calculation error:', error);
        alert('خطأ في الحساب: ' + error.message);
        return;
    }
    
    // Validate calculation result
    if (!calculationResult) {
        alert('خطأ في نتائج الحساب');
        return;
    }
    
    const today = new Date();
    const printDate = today.toLocaleDateString('ar-SA');
    
    // Safe date formatting
    let startDateFormatted, endDateFormatted;
    try {
        if (calculationResult.startDate && calculationResult.endDate) {
            startDateFormatted = calculationResult.startDate.toLocaleDateString('ar-SA');
            endDateFormatted = calculationResult.endDate.toLocaleDateString('ar-SA');
        } else {
            startDateFormatted = startDateInput.value;
            endDateFormatted = endDateInput.value;
        }
    } catch (error) {
        console.error('Date formatting error:', error);
        startDateFormatted = startDateInput.value;
        endDateFormatted = endDateInput.value;
    }

    // Generate detailed calculation breakdown
    let calculationDetails = '';
    
    if (calculationResult.calculationDetails && calculationResult.calculationDetails.length > 0) {
        calculationResult.calculationDetails.forEach(detail => {
            if (detail.year) {
                calculationDetails += `<div class="calc-box">
                    <div class="calc-formula">راتب ${detail.year}: ${formatCurrency(detail.salary)} × ${currentAllowance.percentage || currentAllowance.value}% × ${calculationResult.totalDays} يوم ÷ 30</div>
                    <div class="calc-result">${formatCurrency(detail.allowance)}</div>
                </div>`;
            } else if (detail.amount) {
                calculationDetails += `<div class="calc-box">
                    <div class="calc-formula">${formatCurrency(detail.amount)} × ${detail.days} يوم</div>
                    <div class="calc-result">${formatCurrency(detail.allowance)}</div>
                </div>`;
            } else {
                // Check if it's fixed amount or percentage
                if (currentAllowance.type === 'fixed') {
                    calculationDetails += `<div class="calc-box">
                        <div class="calc-formula">${formatCurrency(currentAllowance.value)} × ${calculationResult.totalDays} يوم ÷ 30 = ${formatCurrency(detail.allowance)}</div>
                    </div>`;
                } else {
                    calculationDetails += `<div class="calc-box">
                        <div class="calc-formula">${formatCurrency(detail.salary)} × ${currentAllowance.percentage || currentAllowance.value}% × ${calculationResult.totalDays} يوم ÷ 30</div>
                        <div class="calc-result">${formatCurrency(detail.allowance)}</div>
                    </div>`;
                }
            }
        });
    } else if (calculationResult.yearlyCalculations && calculationResult.yearlyCalculations.length > 0) {
        // Use yearlyCalculations if available
        calculationResult.yearlyCalculations.forEach(calc => {
            if (currentAllowance.type === 'fixed') {
                calculationDetails += `<div class="calc-box">
                    <div class="calc-formula">سنة ${calc.year}: ${formatCurrency(currentAllowance.value)} × ${calc.days} يوم ÷ 30 = ${formatCurrency(calc.allowance)}</div>
                </div>`;
            } else {
                calculationDetails += `<div class="calc-box">
                    <div class="calc-formula">سنة ${calc.year}: ${formatCurrency(calc.salary)} × ${calc.percentage || currentAllowance.value}% × ${calc.days} يوم ÷ 30</div>
                    <div class="calc-result">${formatCurrency(calc.allowance)}</div>
                </div>`;
            }
        });
    } else {
        if (currentAllowance.type === 'fixed') {
            calculationDetails = `<div class="calc-box">
                <div class="calc-formula">مبلغ ثابت: ${formatCurrency(currentAllowance.value)} × ${calculationResult.totalDays} يوم ÷ 30 = ${formatCurrency(calculationResult.totalAllowance)}</div>
            </div>`;
        } else {
            calculationDetails = `<div class="calc-box">
                <div class="calc-formula">حساب بسيط: ${formatCurrency(calculationResult.currentSalary)} × ${currentAllowance.percentage || currentAllowance.value}% × ${calculationResult.totalDays} يوم ÷ 30</div>
                <div class="calc-result">${formatCurrency(calculationResult.totalAllowance)}</div>
            </div>`;
        }
    }

    console.log('Opening print window...');
    const reportWindow = window.open('', '', 'width=900,height=800');
    
    if (!reportWindow) {
        alert('يرجى السماح للنوافذ المنبثقة لطباعة التقرير');
        return;
    }
    
    reportWindow.document.write(`
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تقرير حساب البدل</title>
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                
                body {
                    font-family: 'Cairo', sans-serif;
                    line-height: 1.4;
                    color: #2c3e50;
                    background: white;
                    padding: 15px;
                    font-size: 12px;
                }
                
                .report-container {
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    border: 2px solid #2c3e50;
                    border-radius: 8px;
                    overflow: hidden;
                }
                
                .report-header {
                    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
                    color: white;
                    padding: 15px;
                    text-align: center;
                }
                
                .header-top {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 10px;
                }
                
                .ministry-logo {
                    width: 60px;
                    height: 60px;
                    background: white;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .ministry-logo img {
                    width: 50px;
                    height: 50px;
                    object-fit: contain;
                }
                
                .header-info {
                    flex: 1;
                    text-align: center;
                    margin: 0 15px;
                }
                
                .header-title {
                    font-size: 16px;
                    font-weight: 700;
                    margin-bottom: 5px;
                }
                
                .header-subtitle {
                    font-size: 14px;
                    font-weight: 600;
                    margin-bottom: 3px;
                }
                
                .header-department {
                    font-size: 11px;
                    font-weight: 400;
                    opacity: 0.9;
                }
                
                .report-number {
                    font-size: 10px;
                    text-align: right;
                    line-height: 1.3;
                }
                
                .report-title {
                    background: #f8f9fa;
                    padding: 15px;
                    text-align: center;
                    border-bottom: 2px solid #e9ecef;
                }
                
                .report-title h2 {
                    font-size: 18px;
                    font-weight: 700;
                    color: #2c3e50;
                    margin: 0;
                }
                
                .report-content {
                    padding: 20px;
                }
                
                .section {
                    margin-bottom: 20px;
                    border: 1px solid #e9ecef;
                    border-radius: 6px;
                    overflow: hidden;
                }
                
                .section-header {
                    background: #f8f9fa;
                    padding: 10px 15px;
                    font-weight: 600;
                    font-size: 13px;
                    color: #495057;
                    border-bottom: 1px solid #e9ecef;
                }
                
                .section-content {
                    padding: 15px;
                }
                
                .info-table {
                    width: 100%;
                    border-collapse: collapse;
                }
                
                .info-table td {
                    padding: 8px 12px;
                    border: 1px solid #e9ecef;
                    font-size: 11px;
                }
                
                .info-table .label {
                    background: #f8f9fa;
                    font-weight: 600;
                    width: 25%;
                }
                
                .info-table .value {
                    font-weight: 500;
                    width: 25%;
                }
                
                .calculation-grid {
                    display: grid;
                    grid-template-columns: 1fr;
                    gap: 10px;
                }
                
                .calc-box {
                    background: #f8f9fa;
                    border: 1px solid #e9ecef;
                    border-radius: 4px;
                    padding: 10px;
                    text-align: center;
                }
                
                .calc-formula {
                    font-size: 11px;
                    color: #6c757d;
                    margin-bottom: 5px;
                }
                
                .calc-result {
                    font-size: 13px;
                    font-weight: 600;
                    color: #28a745;
                }
                
                .total-section {
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                    color: white;
                    padding: 20px;
                    text-align: center;
                    border-radius: 8px;
                    margin: 20px 0;
                }
                
                .decorative-border {
                    font-size: 14px;
                    font-weight: 600;
                    margin-bottom: 10px;
                    padding: 8px;
                    border: 2px solid rgba(255,255,255,0.3);
                    border-radius: 4px;
                }
                
                .total-amount {
                    font-size: 24px;
                    font-weight: 700;
                    margin: 10px 0;
                }
                
                .signatures {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 30px;
                    margin: 30px 0;
                    padding: 20px 0;
                    border-top: 1px solid #e9ecef;
                }
                
                .signature-box {
                    text-align: center;
                    padding: 15px;
                    border: 1px solid #e9ecef;
                    border-radius: 4px;
                    background: #f8f9fa;
                }
                
                .footer-info {
                    background: #2c3e50;
                    color: white;
                    padding: 10px;
                    text-align: center;
                    font-size: 10px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                }
                
                @media print {
                    body { padding: 0; }
                    .report-container { border: none; border-radius: 0; }
                }
            </style>
        </head>
        <body>
            <div class="report-container">
                <!-- Header -->
                <div class="report-header">
                    <div class="header-top">
                        <div class="ministry-logo">
                            <img src="assets/images/ministry-logo2.png" alt="شعار وزارة الدفاع">
                        </div>
                        <div class="header-info">
                            <div class="header-title">بسم الله الرحمن الرحيم</div>
                            <div class="header-subtitle">المملكة العربية السعودية - وزارة الدفاع</div>
                            <div class="header-department">فرع الشؤون الإدارية والمالية للقوات البرية بالجنوبية</div>
                        </div>
                        <div class="report-number">
                            رقم: ${Date.now().toString().slice(-6)}<br>
                            التاريخ: ${printDate}
                        </div>
                    </div>
                </div>

                <!-- Report Title -->
                <div class="report-title">
                    <h2>تقرير حساب ${currentAllowance.name}</h2>
                </div>

                <!-- Content -->
                <div class="report-content">
                    <!-- Period Details -->
                    <div class="section">
                        <div class="section-header">تفاصيل فترة الحساب</div>
                        <div class="section-content">
                            <table class="info-table">
                                <tr>
                                    <td class="label">تاريخ البداية</td>
                                    <td class="value">${startDateFormatted}</td>
                                    <td class="label">تاريخ النهاية</td>
                                    <td class="value">${endDateFormatted}</td>
                                </tr>
                                <tr>
                                    <td class="label">إجمالي الأيام</td>
                                    <td class="value">${calculationResult.totalDays || 0} يوم</td>
                                    <td class="label">نوع البدل</td>
                                    <td class="value">${currentAllowance.name}</td>
                                </tr>
                                <tr>
                                    <td class="label">${currentAllowance.type === 'fixed' ? 'المبلغ الشهري' : 'الراتب الحالي'}</td>
                                    <td class="value">${currentAllowance.type === 'fixed' ? formatCurrency((currentAllowance.value || 0) * 30) : formatCurrency(calculationResult.currentSalary || 0)}</td>
                                    <td class="label">طريقة الحساب</td>
                                    <td class="value">${currentAllowance.description}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Calculation Details -->
                    <div class="section">
                        <div class="section-header">تفاصيل الحساب والمعادلات</div>
                        <div class="section-content">
                            <div class="calculation-grid">
                                ${calculationDetails}
                            </div>
                        </div>
                    </div>

                    <!-- Total Amount -->
                    <div class="total-section">
                        <div class="decorative-border">
                            إجمالي الاستحقاق النهائي
                        </div>
                        <div class="total-amount">${formatCurrency(calculationResult.totalAllowance || 0)}</div>
                        <div style="font-size: 10px; margin-top: 5px;">
                            (${(calculationResult.totalAllowance || 0).toFixed(2)} ريال سعودي)
                        </div>
                    </div>

                    <!-- Signatures -->
                    <div class="signatures">
                        <div class="signature-box">
                            <div style="font-weight: bold;">المحاسب المختص</div>
                            <div style="margin-top: 30px; font-size: 10px;">الاسم: _____________________ التوقيع: _______</div>
                        </div>
                        <div class="signature-box">
                            <div style="font-weight: bold;">المدقق المالي</div>
                            <div style="margin-top: 30px; font-size: 10px;">الاسم: _____________________ التوقيع: _______</div>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="footer-info">
                    <span>نظام حساب البدلات الإلكتروني</span>
                    <span>معهد سلاح المشاة - القوات البرية الجنوبية</span>
                    <span>SYS-${Date.now().toString().slice(-6)}</span>
                </div>
            </div>

            <script>
                function formatCurrency(amount) {
                    if (typeof amount !== 'number' || isNaN(amount)) {
                        return '0.00 ر.س';
                    }
                    return new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR',
                        minimumFractionDigits: 2
                    }).format(amount);
                }
                
                window.onload = function() {
                    setTimeout(() => {
                        window.print();
                    }, 500);
                }
            </script>
        </body>
        </html>
    `);
    reportWindow.document.close();
    console.log('Print window created successfully');
}

// Update print function to use the new report
function printResults() {
    console.log('Print button clicked');
    
    const resultsCard = document.getElementById('resultsCard');
    if (!resultsCard || resultsCard.style.display === 'none') {
        alert('يرجى إجراء الحساب أولاً قبل الطباعة');
        return;
    }
    
    try {
        generatePrintableAllowanceReport();
    } catch (error) {
        console.error('Print error:', error);
        alert('حدث خطأ في الطباعة: ' + error.message);
    }
}

// Make functions globally available
window.generatePrintableAllowanceReport = generatePrintableAllowanceReport;
window.printResults = printResults;







