/* Scholarship Page Military Theme */

.scholarship-header-card {
    background: linear-gradient(135deg, var(--military-dark), var(--military-olive), var(--military-green));
    color: var(--military-white);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 
        0 20px 40px var(--shadow-color),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.scholarship-header-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(85, 239, 196, 0.1) 0%, transparent 50%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.scholarship-icon-large {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.9;
    animation: scholarshipPulse 2s ease-in-out infinite;
    color: var(--military-light-green);
    filter: drop-shadow(0 6px 12px rgba(85, 239, 196, 0.4));
    position: relative;
    z-index: 1;
}

@keyframes scholarshipPulse {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.05) rotate(2deg); }
    50% { transform: scale(1.1) rotate(-1deg); }
    75% { transform: scale(1.05) rotate(1deg); }
}

.calculation-card, .results-card, .period-details-card {
    background: linear-gradient(145deg, var(--military-white), #f8f9fa);
    border-radius: 20px;
    box-shadow: 
        0 15px 35px var(--shadow-color),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    margin-bottom: 2rem;
    overflow: hidden;
    border: 1px solid rgba(0, 184, 148, 0.1);
    transition: all 0.3s ease;
}

.calculation-card:hover, .results-card:hover, .period-details-card:hover {
    transform: translateY(-5px);
    box-shadow: 
        0 20px 45px var(--shadow-color),
        0 0 30px rgba(0, 184, 148, 0.15);
}

.card-header {
    background: linear-gradient(135deg, var(--military-olive), var(--military-green));
    color: var(--military-white);
    border-bottom: none;
    padding: 1rem 1.5rem;
    font-weight: 600;
    position: relative;
}

.card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--military-light-green), var(--military-gold));
    animation: progressBar 3s ease-in-out infinite;
}

@keyframes progressBar {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.card-body {
    padding: 1.5rem;
    position: relative;
}

/* Result Items Animation */
.result-item {
    animation: slideInUp 0.5s ease-out;
    padding: 1rem;
    margin: 0.5rem 0;
    border-radius: 10px;
    background: linear-gradient(145deg, #ffffff, #f1f2f6);
    border-left: 4px solid var(--military-green);
    transition: all 0.3s ease;
}

.result-item:hover {
    transform: translateX(10px);
    box-shadow: 0 5px 15px rgba(0, 184, 148, 0.2);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Total Result Special Styling */
.total-result {
    background: linear-gradient(135deg, var(--military-green), var(--military-light-green)) !important;
    color: var(--military-white) !important;
    border: none !important;
    animation: totalGlow 2s ease-in-out infinite;
    position: relative;
    overflow: hidden;
}

.total-result::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shine 3s ease-in-out infinite;
}

@keyframes totalGlow {
    0%, 100% { box-shadow: 0 5px 15px rgba(0, 184, 148, 0.3); }
    50% { box-shadow: 0 8px 25px rgba(0, 184, 148, 0.5); }
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Ministry Logo for dark backgrounds */
.ministry-logo-white {
    max-width: 100px;
    height: auto;
    filter: brightness(0) invert(1) drop-shadow(0 4px 8px rgba(255, 255, 255, 0.3));
    object-fit: contain;
    animation: logoFloat 3s ease-in-out infinite;
    position: relative;
    z-index: 1;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Info Alert Military Styling */
.alert-info {
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.1), rgba(0, 184, 148, 0.1));
    border: 2px solid var(--military-green);
    border-radius: 12px;
    color: var(--military-dark);
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

.alert-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: var(--military-green);
    animation: alertPulse 2s ease-in-out infinite;
}

@keyframes alertPulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Responsive Design for Scholarship */
@media (max-width: 1200px) {
    .scholarship-header-card {
        padding: 1.5rem;
    }
    
    .scholarship-icon-large {
        font-size: 3.5rem;
    }
}

@media (max-width: 768px) {
    .scholarship-header-card {
        padding: 1.5rem;
    }
    
    .scholarship-icon-large {
        font-size: 3rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .result-value {
        font-size: 1rem;
    }
    
    .total-result .result-value {
        font-size: 1.1rem;
    }
    
    .ministry-logo-white {
        max-width: 70px;
    }
}

