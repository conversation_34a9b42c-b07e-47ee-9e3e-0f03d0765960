// Global variables
let currentAllowance = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing allowance page...');
    
    // Load allowance data first
    loadAllowanceData();
    
    // Setup event listeners
    setupEventListeners();
    
    // Add a simple test to verify everything is working
    setTimeout(() => {
        console.log('Testing after initialization...');
        console.log('Current allowance:', currentAllowance);
        console.log('Calculate function available:', typeof window.calculateAllowance);
        console.log('Test function available:', typeof window.testYearlyFields);
        
        // Test button availability
        const calculateBtn = document.querySelector('button[onclick*="calculateAllowance"]');
        const testBtn = document.querySelector('button[onclick*="testYearlyFields"]');
        console.log('Buttons found:', {
            calculate: !!calculateBtn,
            test: !!testBtn
        });
        
    }, 1000);
});

// Load allowance data from URL parameter
function loadAllowanceData() {
    const urlParams = new URLSearchParams(window.location.search);
    const allowanceId = urlParams.get('id');
    
    if (!allowanceId) {
        window.location.href = 'index.html';
        return;
    }
    
    // Get allowances from localStorage
    const allowances = JSON.parse(localStorage.getItem('allowances') || '[]');
    currentAllowance = allowances.find(a => a.id === allowanceId);
    
    if (!currentAllowance) {
        alert('البدل المطلوب غير موجود');
        window.location.href = 'index.html';
        return;
    }
    
    console.log('Loaded allowance:', currentAllowance); // للتأكد من التحميل
    renderAllowanceHeader();
}

// Render allowance header
function renderAllowanceHeader() {
    const header = document.getElementById('allowanceHeader');
    const typeText = currentAllowance.type === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت';
    const valueText = currentAllowance.type === 'percentage' 
        ? `${currentAllowance.value}%` 
        : `${currentAllowance.value} ريال/يوم`;
    
    header.innerHTML = `
        <div class="allowance-icon-large">
            <i class="${currentAllowance.icon}"></i>
        </div>
        <h2>${currentAllowance.name}</h2>
        <p class="lead mb-3">${currentAllowance.description}</p>
        <div class="row">
            <div class="col-md-6">
                <div class="text-center">
                    <small>نوع الحساب</small>
                    <div class="h5">${typeText}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="text-center">
                    <small>القيمة</small>
                    <div class="h5">${valueText}</div>
                </div>
            </div>
        </div>
    `;
}

// Setup event listeners
function setupEventListeners() {
    console.log('Setting up event listeners...');
    
    // Auto-calculate when inputs change
    const inputs = ['startDate', 'endDate', 'currentSalary', 'previousSalary'];
    inputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', handleInputChange);
            element.addEventListener('input', handleInputChange);
            console.log(`Added listeners to ${id}`);
        }
    });
    
    // Add button event listeners using IDs
    const calculateBtn = document.getElementById('calculateBtn');
    if (calculateBtn) {
        calculateBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Calculate button clicked');
            calculateAllowance();
        });
        console.log('Added calculate button listener');
    }
    
    const testBtn = document.getElementById('testBtn');
    if (testBtn) {
        testBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Test button clicked');
            testYearlyFields();
        });
        console.log('Added test button listener');
    }
    
    // Set default dates
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    
    if (startDateEl) startDateEl.value = formatDate(firstDayOfMonth);
    if (endDateEl) endDateEl.value = formatDate(today);
    
    console.log('Event listeners setup complete');
}

// Handle input changes
function handleInputChange(event) {
    // Don't trigger field regeneration if the change is from a yearly salary input
    const isYearlySalaryInput = event && event.target && event.target.classList.contains('yearly-salary-input');
    
    if (!isYearlySalaryInput) {
        // Check if we need to show yearly salary fields
        checkAndShowYearlySalaryFields();
    }
    
    // Auto calculate with debounce
    clearTimeout(window.calculateTimeout);
    window.calculateTimeout = setTimeout(() => {
        if (isFormValid()) {
            calculateAllowance();
        }
    }, 500);
}

// Check if we need yearly salary fields
function checkAndShowYearlySalaryFields() {
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    
    if (!startDateEl || !endDateEl || !currentAllowance) return;
    
    const startDate = startDateEl.value;
    const endDate = endDateEl.value;
    
    if (!startDate || !endDate) return;
    
    const isCurrentSalaryBased = currentAllowance.description === 'من المربوط الحالي';
    const yearlySalariesContainer = document.getElementById('yearlySalariesContainer');
    const previousSalaryField = document.getElementById('previousSalaryField');
    
    console.log('Checking yearly fields:', {
        isCurrentSalaryBased,
        startDate,
        endDate,
        hasYearlyFields: !!yearlySalariesContainer
    });
    
    if (isCurrentSalaryBased) {
        const startYear = new Date(startDate).getFullYear();
        const endYear = new Date(endDate).getFullYear();
        const yearsDiff = endYear - startYear + 1;
        
        console.log('Years difference:', yearsDiff);
        
        if (yearsDiff > 1) {
            // Multiple years - show yearly salary fields
            const years = [];
            for (let year = startYear; year <= endYear; year++) {
                years.push(year);
            }
            
            generateYearlySalaryFields(years);
            if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'block';
            if (previousSalaryField) previousSalaryField.style.display = 'none';
        } else {
            // Single year - hide yearly fields
            if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'none';
            if (previousSalaryField) previousSalaryField.style.display = 'block';
        }
    } else {
        // Normal calculation - show previous salary field
        if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'none';
        if (previousSalaryField) previousSalaryField.style.display = 'block';
    }
}

// Generate yearly salary fields
function generateYearlySalaryFields(years) {
    const container = document.getElementById('yearlySalariesFields');
    if (!container) {
        console.error('yearlySalariesFields container not found');
        return;
    }
    
    console.log('Generating fields for years:', years);
    
    // Check if fields already exist for these years
    const existingFields = container.querySelectorAll('.yearly-salary-field');
    const existingYears = Array.from(existingFields).map(field => {
        const input = field.querySelector('input');
        return input ? parseInt(input.dataset.year) : null;
    }).filter(year => year !== null);
    
    // If the same years already exist, don't regenerate
    if (existingYears.length === years.length && 
        years.every(year => existingYears.includes(year))) {
        console.log('Fields already exist for these years, skipping generation');
        return;
    }
    
    // Store existing values before clearing
    const existingValues = {};
    existingFields.forEach(field => {
        const input = field.querySelector('input');
        if (input && input.value) {
            existingValues[input.dataset.year] = input.value;
        }
    });
    
    // Clear existing fields
    container.innerHTML = '';
    
    const currentYear = new Date().getFullYear();
    const currentSalaryEl = document.getElementById('currentSalary');
    const currentSalaryValue = currentSalaryEl ? currentSalaryEl.value : '';
    
    years.forEach((year, index) => {
        const fieldDiv = document.createElement('div');
        fieldDiv.className = 'mb-3 yearly-salary-field';
        
        let yearLabel = `راتب سنة ${year}`;
        let placeholder = `أدخل راتب سنة ${year}`;
        let defaultValue = '';
        
        // Restore existing value if available
        if (existingValues[year]) {
            defaultValue = existingValues[year];
        } else if (year === currentYear) {
            yearLabel += ' (السنة الحالية)';
            defaultValue = currentSalaryValue;
        } else if (year < currentYear) {
            yearLabel += ' (سنة سابقة)';
        } else {
            yearLabel += ' (سنة مستقبلية)';
        }
        
        fieldDiv.innerHTML = `
            <label for="salary_${year}" class="form-label">
                <i class="fas fa-calendar-year me-2"></i>
                ${yearLabel}
            </label>
            <input type="number" 
                   class="form-control yearly-salary-input" 
                   id="salary_${year}" 
                   data-year="${year}"
                   placeholder="${placeholder}"
                   min="0" 
                   step="0.01"
                   value="${defaultValue}">
        `;
        
        container.appendChild(fieldDiv);
        
        // Add event listener to the new field
        const input = fieldDiv.querySelector('input');
        if (input) {
            input.addEventListener('change', handleInputChange);
            input.addEventListener('input', handleInputChange);
        }
    });
    
    console.log(`Generated ${years.length} yearly salary fields`);
}

// Auto calculate with debounce
function autoCalculate() {
    clearTimeout(calculateTimeout);
    calculateTimeout = setTimeout(() => {
        if (isFormValid()) {
            calculateAllowance();
        }
    }, 500);
}

// Check if form is valid - Enhanced
function isFormValid() {
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    const currentSalaryEl = document.getElementById('currentSalary');
    
    if (!startDateEl || !endDateEl || !currentSalaryEl) {
        console.log('Required form elements not found');
        return false;
    }
    
    const startDate = startDateEl.value;
    const endDate = endDateEl.value;
    const currentSalary = currentSalaryEl.value;
    
    console.log('Validating form:', { startDate, endDate, currentSalary });
    
    if (!startDate || !endDate || !currentSalary || parseFloat(currentSalary) <= 0) {
        console.log('Basic validation failed');
        return false;
    }
    
    // Check if currentAllowance is loaded
    if (!currentAllowance) {
        console.log('Current allowance not loaded');
        return false;
    }
    
    return true;
}

// Make functions globally available
window.calculateAllowance = calculateAllowance;
window.printResults = printResults;
window.exportToPDF = exportToPDF;

// Test yearly fields function
window.testYearlyFields = function() {
    console.log("Testing yearly fields function...");
    
    // Check if we need to show yearly salary fields
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    
    if (!startDateEl || !endDateEl) {
        console.error("Date fields not found");
        return;
    }
    
    // Set some test dates (multi-year period)
    const today = new Date();
    const lastYear = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());
    
    startDateEl.value = formatDate(lastYear);
    endDateEl.value = formatDate(today);
    
    // Trigger change event to show yearly fields
    if (typeof handleInputChange === 'function') {
        handleInputChange({ target: startDateEl });
    }
    
    alert("تم تعيين تواريخ اختبار لفترة متعددة السنوات. يجب أن تظهر حقول الرواتب السنوية الآن.");
};

// Calculate allowance function
function calculateAllowance() {
    console.log('Calculate button clicked');
    
    if (!isFormValid()) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // Show calculating state
    document.body.classList.add('calculating');
    
    setTimeout(() => {
        try {
            const result = performCalculation();
            displayResults(result);
            displayPeriodDetails(result.periodDetails);
        } catch (error) {
            console.error('Calculation error:', error);
            alert('حدث خطأ في الحساب: ' + error.message);
        } finally {
            document.body.classList.remove('calculating');
        }
    }, 500);
}

// Perform the actual calculation - Fixed
function performCalculation() {
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    const currentSalaryEl = document.getElementById('currentSalary');
    
    if (!startDateEl || !endDateEl || !currentSalaryEl) {
        throw new Error('عناصر النموذج غير موجودة');
    }
    
    if (!startDateEl.value || !endDateEl.value || !currentSalaryEl.value) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
    }
    
    const startDate = new Date(startDateEl.value);
    const endDate = new Date(endDateEl.value);
    const currentSalary = parseFloat(currentSalaryEl.value);
    
    // Validate dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new Error('التواريخ المدخلة غير صحيحة');
    }
    
    if (startDate > endDate) {
        throw new Error('تاريخ البداية يجب أن يكون قبل تاريخ النهاية');
    }
    
    if (!currentAllowance) {
        throw new Error('لم يتم تحديد نوع البدل');
    }
    
    // Calculate days
    const timeDiff = endDate.getTime() - startDate.getTime();
    const totalDays = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
    
    // Calculate allowance based on type
    let totalAllowance = 0;
    let calculationDetails = [];
    
    if (currentAllowance.description === 'من المربوط الحالي') {
        // Use yearly salary fields if available
        const yearlySalariesContainer = document.getElementById('yearlySalariesContainer');
        if (yearlySalariesContainer && yearlySalariesContainer.style.display !== 'none') {
            const yearlyInputs = document.querySelectorAll('.yearly-salary-input');
            if (yearlyInputs.length > 0) {
                yearlyInputs.forEach(input => {
                    const year = input.dataset.year;
                    const salary = parseFloat(input.value);
                    if (!isNaN(salary) && salary > 0) {
                        const yearAllowance = (salary * currentAllowance.percentage / 100) * (totalDays / 360);
                        totalAllowance += yearAllowance;
                        calculationDetails.push({
                            year: year,
                            salary: salary,
                            allowance: yearAllowance
                        });
                    }
                });
            } else {
                // Fallback to current salary
                totalAllowance = (currentSalary * currentAllowance.percentage / 100) * (totalDays / 360);
                calculationDetails.push({
                    salary: currentSalary,
                    allowance: totalAllowance
                });
            }
        } else {
            // Simple calculation with current salary
            totalAllowance = (currentSalary * currentAllowance.percentage / 100) * (totalDays / 360);
            calculationDetails.push({
                salary: currentSalary,
                allowance: totalAllowance
            });
        }
    } else {
        // Fixed amount calculation
        totalAllowance = currentAllowance.amount * totalDays;
        calculationDetails.push({
            amount: currentAllowance.amount,
            days: totalDays,
            allowance: totalAllowance
        });
    }
    
    return {
        startDate: startDate,
        endDate: endDate,
        totalDays: totalDays,
        currentSalary: currentSalary,
        totalAllowance: totalAllowance,
        calculationDetails: calculationDetails,
        allowanceType: currentAllowance,
        periodDetails: {
            startDate: startDate.toLocaleDateString('ar-SA'),
            endDate: endDate.toLocaleDateString('ar-SA'),
            totalDays: totalDays
        }
    };
}

// Calculate period details (30 days per month, 360 days per year)
function calculatePeriodDetails(startDate, endDate) {
    const startYear = startDate.getFullYear();
    const startMonth = startDate.getMonth() + 1;
    const startDay = startDate.getDate();
    
    const endYear = endDate.getFullYear();
    const endMonth = endDate.getMonth() + 1;
    const endDay = endDate.getDate();
    
    // Calculate total days using 30-day month system
    const totalDays = calculateDaysBetween(startDate, endDate);
    
    // Get years in the period
    const years = [];
    for (let year = startYear; year <= endYear; year++) {
        const yearStartDate = year === startYear ? startDate : new Date(year, 0, 1);
        const yearEndDate = year === endYear ? endDate : new Date(year, 11, 30);
        
        const yearDays = calculateDaysBetween(yearStartDate, yearEndDate);
        
        years.push({
            year: year,
            days: yearDays,
            startDate: yearStartDate,
            endDate: yearEndDate
        });
    }
    
    return {
        totalDays: totalDays,
        years: years,
        startDate: startDate,
        endDate: endDate
    };
}

// Calculate days between two dates (30 days per month system)
function calculateDaysBetween(start, end) {
    const startYear = start.getFullYear();
    const startMonth = start.getMonth() + 1; // Convert to 1-12
    const startDay = start.getDate();
    
    const endYear = end.getFullYear();
    const endMonth = end.getMonth() + 1; // Convert to 1-12
    const endDay = end.getDate();
    
    // Ensure days don't exceed 30 in our system
    const adjustedStartDay = Math.min(startDay, 30);
    const adjustedEndDay = Math.min(endDay, 30);
    
    // Calculate total months
    const totalStartMonths = (startYear * 12) + startMonth;
    const totalEndMonths = (endYear * 12) + endMonth;
    
    let totalDays = 0;
    
    if (totalStartMonths === totalEndMonths) {
        // Same month
        totalDays = adjustedEndDay - adjustedStartDay + 1;
    } else {
        // Different months
        // Days remaining in start month
        totalDays += (30 - adjustedStartDay + 1);
        
        // Full months between
        const monthsBetween = totalEndMonths - totalStartMonths - 1;
        totalDays += monthsBetween * 30;
        
        // Days in end month
        totalDays += adjustedEndDay;
    }
    
    return Math.max(0, totalDays);
}

// Display calculation results
function displayResults(result) {
    const resultsCard = document.getElementById('resultsCard');
    const resultsContent = document.getElementById('resultsContent');
    
    let html = '';
    
    // Yearly breakdown if multiple years
    if (result.yearlyCalculations.length > 1) {
        html += '<h6 class="mb-3">التفصيل السنوي:</h6>';
        result.yearlyCalculations.forEach(calc => {
            html += `
                <div class="result-item">
                    <div class="result-label">سنة ${calc.year} (${calc.days} يوم)</div>
                    <div class="result-value">${formatCurrency(calc.allowance)}</div>
                    <div class="result-details">
                        الراتب: ${formatCurrency(calc.salary)}
                        ${calc.isCurrentSalaryBased ? ' (من المربوط الحالي)' : ''}
                    </div>
                </div>
            `;
        });
        
        html += '<hr class="my-3">';
    }
    
    // Total result
    html += `
        <div class="total-result">
            <div class="total-label">إجمالي البدل</div>
            <div class="total-value">${formatCurrency(result.totalAllowance)}</div>
            <div class="total-details">
                ${result.allowanceInfo.name} - ${result.totalDays} يوم
            </div>
        </div>
    `;
    
    resultsContent.innerHTML = html;
    resultsCard.style.display = 'block';
    
    // Scroll to results
    resultsCard.scrollIntoView({ behavior: 'smooth' });
}

// Display period details
function displayPeriodDetails(periodDetails) {
    const detailsCard = document.getElementById('periodDetails');
    const detailsContent = document.getElementById('periodDetailsContent');
    
    const html = `
        <div class="period-item">
            <span class="period-label">تاريخ البداية</span>
            <span class="period-value">${formatDateArabic(periodDetails.startDate)}</span>
        </div>
        <div class="period-item">
            <span class="period-label">تاريخ النهاية</span>
            <span class="period-value">${formatDateArabic(periodDetails.endDate)}</span>
        </div>
        <div class="period-item">
            <span class="period-label">إجمالي الأيام</span>
            <span class="period-value">${periodDetails.totalDays} يوم</span>
        </div>
        <div class="period-item">
            <span class="period-label">عدد السنوات</span>
            <span class="period-value">${periodDetails.years.length} سنة</span>
        </div>
        <div class="period-item">
            <span class="period-label">طريقة الحساب</span>
            <span class="period-value">30 يوم لكل شهر</span>
        </div>
    `;
    
    detailsContent.innerHTML = html;
    detailsCard.style.display = 'block';
}

// Utility functions
function formatCurrency(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) {
        return '0.00 ر.س';
    }
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatDate(date) {
    if (!date || !(date instanceof Date)) {
        return '';
    }
    return date.toISOString().split('T')[0];
}

function formatDateArabic(date) {
    if (!date || !(date instanceof Date)) {
        return 'غير محدد';
    }
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(date);
}

// Make utility functions globally available
window.formatCurrency = formatCurrency;
window.formatDate = formatDate;
window.formatDateArabic = formatDateArabic;

// Print results
function printResults() {
    window.print();
}

// Export to PDF
function exportToPDF() {
    if (!validateFormForReport()) {
        return;
    }

    // Validate form function
    function validateFormForReport() {
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');
        const currentSalary = document.getElementById('currentSalary');
        
        if (!startDate || !endDate || !currentSalary) {
            alert('يرجى التأكد من تعبئة جميع الحقول');
            return false;
        }
        
        if (!startDate.value || !endDate.value || !currentSalary.value) {
            alert('يرجى تعبئة جميع الحقول المطلوبة');
            return false;
        }
        
        return true;
    }
    
    if (typeof jsPDF === 'undefined') {
        alert('مكتبة PDF غير متوفرة');
        return;
    }
    
    if (!currentAllowance) {
        alert('لم يتم تحديد نوع البدل');
        return;
    }
    
    const allowanceSelect = document.getElementById('allowanceSelect');
    const selectedOption = allowanceSelect ? allowanceSelect.querySelector(`option[value="${currentAllowance.id}"]`) : null;
    const allowanceName = selectedOption ? selectedOption.textContent : currentAllowance.name;
    const allowanceDescription = selectedOption ? selectedOption.getAttribute('data-description') : currentAllowance.description;
    const allowanceType = selectedOption ? selectedOption.getAttribute('data-type') : currentAllowance.type;
    const allowanceValue = selectedOption ? selectedOption.getAttribute('data-value') : currentAllowance.value;
    const allowanceIcon = selectedOption ? selectedOption.getAttribute('data-icon') : currentAllowance.icon;

    // Update allowance header
    const allowanceHeader = document.getElementById('allowanceHeader');
    if (allowanceHeader) {
        allowanceHeader.innerHTML = `
            <div class="allowance-icon-large">
                <i class="${allowanceIcon || 'fas fa-calculator'}"></i>
            </div>
            <h2>${allowanceName}</h2>
            <p class="lead mb-3">${allowanceDescription}</p>
            <div class="row">
                <div class="col-md-6">
                    <div class="text-center">
                        <small>نوع الحساب</small>
                        <div class="h5">${allowanceType === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت'}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="text-center">
                        <small>القيمة</small>
                        <div class="h5">${allowanceType === 'percentage' ? `${allowanceValue}%` : `${allowanceValue} ريال/يوم`}</div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // Generate PDF
    try {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();
        
        doc.setFont('helvetica');
        doc.setFontSize(16);
        
        doc.text('تقرير حساب البدل', 105, 20, { align: 'center' });
        
        doc.save('تقرير-البدل.pdf');
    } catch (error) {
        console.error('PDF export error:', error);
        alert('حدث خطأ في تصدير PDF: ' + error.message);
    }
}

// Test function for date calculations (for debugging)
function testDateCalculations() {
    console.log('Testing 30-day month system:');
    
    // Test 1: Full year should be 360 days
    const jan1 = new Date(2024, 0, 1);
    const dec30 = new Date(2024, 11, 30);
    console.log(`Full year 2024: ${calculateDaysBetween(jan1, dec30)} days (should be 360)`);
    
    // Test 2: Full month should be 30 days
    const month1 = new Date(2024, 0, 1);
    const month30 = new Date(2024, 0, 30);
    console.log(`Full month January 2024: ${calculateDaysBetween(month1, month30)} days (should be 30)`);
    
    // Test 3: Multiple years
    const jan1_2023 = new Date(2023, 0, 1);
    const dec30_2024 = new Date(2024, 11, 30);
    console.log(`Multiple years (2023-2024): ${calculateDaysBetween(jan1_2023, dec30_2024)} days (should be 720)`);
}

// Select allowance and update form
function selectAllowance(allowanceId) {
    currentAllowance = allowances.find(a => a.id === allowanceId);
    
    if (!currentAllowance) {
        console.error('Allowance not found:', allowanceId);
        return;
    }
    
    console.log('Selected allowance:', currentAllowance);
    
    // Update UI
    updateAllowanceDisplay();
    
    // Check if we need yearly salary fields
    setTimeout(() => {
        checkAndShowYearlySalaryFields();
    }, 100);
    
    // Auto calculate if form is valid
    if (isFormValid()) {
        calculateAllowance();
    }
}

// Update allowance display
function updateAllowanceDisplay() {
    const allowanceSelect = document.getElementById('allowanceSelect');
    const selectedOption = allowanceSelect.querySelector(`option[value="${currentAllowance.id}"]`);
    const allowanceName = selectedOption.textContent;
    const allowanceDescription = selectedOption.getAttribute('data-description');
    const allowanceType = selectedOption.getAttribute('data-type');
    const allowanceValue = selectedOption.getAttribute('data-value');
    const allowanceIcon = selectedOption.getAttribute('data-icon');

    // Update allowance header
    const allowanceHeader = document.getElementById('allowanceHeader');
    allowanceHeader.innerHTML = `
        <div class="allowance-icon-large">
            <i class="${allowanceIcon}"></i>
        </div>
        <h2>${allowanceName}</h2>
        <p class="lead mb-3">${allowanceDescription}</p>
        <div class="row">
            <div class="col-md-6">
                <div class="text-center">
                    <small>نوع الحساب</small>
                    <div class="h5">${allowanceType === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت'}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="text-center">
                    <small>القيمة</small>
                    <div class="h5">${allowanceType === 'percentage' ? `${allowanceValue}%` : `${allowanceValue} ريال/يوم`}</div>
                </div>
            </div>
        </div>
    `;
}

// Setup event listeners
function setupEventListeners() {
    console.log('Setting up event listeners...');
    
    // Auto-calculate when inputs change
    const inputs = ['startDate', 'endDate', 'currentSalary', 'previousSalary'];
    inputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', handleInputChange);
            element.addEventListener('input', handleInputChange);
            console.log(`Added listeners to ${id}`);
        }
    });
    
    // Add button event listeners using IDs
    const calculateBtn = document.getElementById('calculateBtn');
    if (calculateBtn) {
        calculateBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Calculate button clicked');
            calculateAllowance();
        });
        console.log('Added calculate button listener');
    }
    
    const testBtn = document.getElementById('testBtn');
    if (testBtn) {
        testBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Test button clicked');
            testYearlyFields();
        });
        console.log('Added test button listener');
    }
    
    // Set default dates
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    
    if (startDateEl) startDateEl.value = formatDate(firstDayOfMonth);
    if (endDateEl) endDateEl.value = formatDate(today);
    
    console.log('Event listeners setup complete');
}

// Handle input changes
function handleInputChange() {
    // Check if we need to show yearly salary fields
    checkAndShowYearlySalaryFields();
    
    // Auto calculate with debounce
    clearTimeout(window.calculateTimeout);
    window.calculateTimeout = setTimeout(() => {
        if (isFormValid()) {
            calculateAllowance();
        }
    }, 500);
}

// Check if we need yearly salary fields
function checkAndShowYearlySalaryFields() {
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    
    if (!startDateEl || !endDateEl || !currentAllowance) return;
    
    const startDate = startDateEl.value;
    const endDate = endDateEl.value;
    
    if (!startDate || !endDate) return;
    
    const isCurrentSalaryBased = currentAllowance.description === 'من المربوط الحالي';
    const yearlySalariesContainer = document.getElementById('yearlySalariesContainer');
    const previousSalaryField = document.getElementById('previousSalaryField');
    
    console.log('Checking yearly fields:', {
        isCurrentSalaryBased,
        startDate,
        endDate,
        hasYearlyFields: !!yearlySalariesContainer
    });
    
    if (isCurrentSalaryBased) {
        const startYear = new Date(startDate).getFullYear();
        const endYear = new Date(endDate).getFullYear();
        const yearsDiff = endYear - startYear + 1;
        
        console.log('Years difference:', yearsDiff);
        
        if (yearsDiff > 1) {
            // Multiple years - show yearly salary fields
            const years = [];
            for (let year = startYear; year <= endYear; year++) {
                years.push(year);
            }
            
            generateYearlySalaryFields(years);
            if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'block';
            if (previousSalaryField) previousSalaryField.style.display = 'none';
        } else {
            // Single year - hide yearly fields
            if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'none';
            if (previousSalaryField) previousSalaryField.style.display = 'block';
        }
    } else {
        // Normal calculation - show previous salary field
        if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'none';
        if (previousSalaryField) previousSalaryField.style.display = 'block';
    }
}

// Generate yearly salary fields
function generateYearlySalaryFields(years) {
    const container = document.getElementById('yearlySalariesFields');
    if (!container) {
        console.error('yearlySalariesFields container not found');
        return;
    }
    
    console.log('Generating fields for years:', years);
    
    // Check if fields already exist for these years
    const existingFields = container.querySelectorAll('.yearly-salary-field');
    const existingYears = Array.from(existingFields).map(field => {
        const input = field.querySelector('input');
        return input ? parseInt(input.dataset.year) : null;
    }).filter(year => year !== null);
    
    // If the same years already exist, don't regenerate
    if (existingYears.length === years.length && 
        years.every(year => existingYears.includes(year))) {
        console.log('Fields already exist for these years, skipping generation');
        return;
    }
    
    // Store existing values before clearing
    const existingValues = {};
    existingFields.forEach(field => {
        const input = field.querySelector('input');
        if (input && input.value) {
            existingValues[input.dataset.year] = input.value;
        }
    });
    
    // Clear existing fields
    container.innerHTML = '';
    
    const currentYear = new Date().getFullYear();
    const currentSalaryEl = document.getElementById('currentSalary');
    const currentSalaryValue = currentSalaryEl ? currentSalaryEl.value : '';
    
    years.forEach((year, index) => {
        const fieldDiv = document.createElement('div');
        fieldDiv.className = 'mb-3 yearly-salary-field';
        
        let yearLabel = `راتب سنة ${year}`;
        let placeholder = `أدخل راتب سنة ${year}`;
        let defaultValue = '';
        
        // Restore existing value if available
        if (existingValues[year]) {
            defaultValue = existingValues[year];
        } else if (year === currentYear) {
            yearLabel += ' (السنة الحالية)';
            defaultValue = currentSalaryValue;
        } else if (year < currentYear) {
            yearLabel += ' (سنة سابقة)';
        } else {
            yearLabel += ' (سنة مستقبلية)';
        }
        
        fieldDiv.innerHTML = `
            <label for="salary_${year}" class="form-label">
                <i class="fas fa-calendar-year me-2"></i>
                ${yearLabel}
            </label>
            <input type="number" 
                   class="form-control yearly-salary-input" 
                   id="salary_${year}" 
                   data-year="${year}"
                   placeholder="${placeholder}"
                   min="0" 
                   step="0.01"
                   value="${defaultValue}">
        `;
        
        container.appendChild(fieldDiv);
        
        // Add event listener to the new field
        const input = fieldDiv.querySelector('input');
        if (input) {
            input.addEventListener('change', handleInputChange);
            input.addEventListener('input', handleInputChange);
        }
    });
    
    console.log(`Generated ${years.length} yearly salary fields`);
}

// Auto calculate with debounce
function autoCalculate() {
    clearTimeout(calculateTimeout);
    calculateTimeout = setTimeout(() => {
        if (isFormValid()) {
            calculateAllowance();
        }
    }, 500);
}

// Check if form is valid
function isFormValid() {
    const startDate = document.getElementById('startDate')?.value;
    const endDate = document.getElementById('endDate')?.value;
    const currentSalary = document.getElementById('currentSalary')?.value;
    
    console.log('Validating form:', { startDate, endDate, currentSalary });
    
    if (!startDate || !endDate || !currentSalary || parseFloat(currentSalary) <= 0) {
        console.log('Basic validation failed');
        return false;
    }
    
    // Check yearly salary fields if they are visible
    const isCurrentSalaryBased = currentAllowance && currentAllowance.description === 'من المربوط الحالي';
    const yearlySalariesContainer = document.getElementById('yearlySalariesContainer');
    
    if (isCurrentSalaryBased && yearlySalariesContainer && yearlySalariesContainer.style.display !== 'none') {
        const yearlyInputs = document.querySelectorAll('.yearly-salary-input');
        for (let input of yearlyInputs) {
            const value = parseFloat(input.value);
            if (!input.value || value <= 0) {
                console.log(`Validation failed for year ${input.dataset.year}`);
                return false;
            }
        }
    }
    
    return true;
}

// Calculate allowance
function calculateAllowance() {
    if (!isFormValid()) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // Show calculating state
    document.body.classList.add('calculating');
    
    setTimeout(() => {
        try {
            const result = performCalculation();
            displayResults(result);
            displayPeriodDetails(result.periodDetails);
        } catch (error) {
            alert('حدث خطأ في الحساب: ' + error.message);
        } finally {
            document.body.classList.remove('calculating');
        }
    }, 500);
}

// Perform the actual calculation
function performCalculation() {
    const startDateElement = document.getElementById('startDate');
    const endDateElement = document.getElementById('endDate');
    const currentSalaryElement = document.getElementById('currentSalary');
    const previousSalaryElement = document.getElementById('previousSalary');

    if (!startDateElement || !endDateElement || !currentSalaryElement) {
        throw new Error('عنصر من عناصر النموذج غير موجود');
    }

    const startDate = new Date(startDateElement.value);
    const endDate = new Date(endDateElement.value);
    const currentSalary = parseFloat(currentSalaryElement.value);
    const previousSalary = previousSalaryElement ? parseFloat(previousSalaryElement.value) || currentSalary : currentSalary;
    
    
    // Validate dates
    if (endDate <= startDate) {
        throw new Error('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
    }
    
    // Calculate period details
    const periodDetails = calculatePeriodDetails(startDate, endDate);
    
    // Check if this is "من المربوط الحالي" calculation
    const isCurrentSalaryBased = currentAllowance.description === 'من المربوط الحالي';
    
    // Get yearly salaries if needed
    const yearlySalaries = {};
    if (isCurrentSalaryBased && periodDetails.years.length > 1) {
        // Collect yearly salaries from dynamic fields
        let hasEmptyFields = false;
        
        periodDetails.years.forEach(yearData => {
            const salaryInput = document.getElementById(`salary_${yearData.year}`);
            if (salaryInput) {
                const salary = parseFloat(salaryInput.value);
                if (!salary || salary <= 0) {
                    hasEmptyFields = true;
                } else {
                    yearlySalaries[yearData.year] = salary;
                }
            }
        });
        
        if (hasEmptyFields) {
            throw new Error('يرجى إدخال راتب صحيح لجميع السنوات');
        }
    }
    
    // Calculate allowance for each year
    let totalAllowance = 0;
    const yearlyCalculations = [];
    
    periodDetails.years.forEach(yearData => {
        let salary;
        
        if (isCurrentSalaryBased && periodDetails.years.length > 1) {
            // Use yearly salaries from dynamic fields
            salary = yearlySalaries[yearData.year];
            if (!salary) {
                throw new Error(`لم يتم العثور على راتب سنة ${yearData.year}`);
            }
        } else if (isCurrentSalaryBased) {
            // Single year - use current salary
            salary = currentSalary;
        } else {
            // Normal calculation - use current salary for current year, previous for others
            const currentYear = new Date().getFullYear();
            salary = yearData.year === currentYear ? currentSalary : previousSalary;
        }
        
        let yearlyAllowance = 0;
        
        if (currentAllowance.type === 'percentage') {
            // Percentage calculation: salary × percentage ÷ 100 × days ÷ 365
            yearlyAllowance = (salary * currentAllowance.value / 100) * (yearData.days / 365);
        } else if (currentAllowance.type === 'fixed') {
            // Fixed amount calculation: fixed amount × days ÷ 365
            yearlyAllowance = currentAllowance.value * (yearData.days / 365);
        }
        
        totalAllowance += yearlyAllowance;
        
        yearlyCalculations.push({
            year: yearData.year,
            salary: salary,
            days: yearData.days,
            allowance: yearlyAllowance,
            percentage: currentAllowance.type === 'percentage' ? currentAllowance.value : null
        });
    });
    
    return {
        totalAllowance: totalAllowance,
        yearlyCalculations: yearlyCalculations,
        periodDetails: periodDetails,
        allowanceInfo: currentAllowance
    };
}

// Calculate period details (30 days per month, 360 days per year)
function calculatePeriodDetails(startDate, endDate) {
    const startYear = startDate.getFullYear();
    const startMonth = startDate.getMonth() + 1;
    const startDay = startDate.getDate();
    
    const endYear = endDate.getFullYear();
    const endMonth = endDate.getMonth() + 1;
    const endDay = endDate.getDate();
    
    // Calculate total days using 30-day month system
    const totalDays = calculateDaysBetween(startDate, endDate);
    
    // Get years in the period
    const years = [];
    for (let year = startYear; year <= endYear; year++) {
        const yearStartDate = year === startYear ? startDate : new Date(year, 0, 1);
        const yearEndDate = year === endYear ? endDate : new Date(year, 11, 30);
        
        const yearDays = calculateDaysBetween(yearStartDate, yearEndDate);
        
        years.push({
            year: year,
            days: yearDays,
            startDate: yearStartDate,
            endDate: yearEndDate
        });
    }
    
    return {
        totalDays: totalDays,
        years: years,
        startDate: startDate,
        endDate: endDate
    };
}

// Calculate days between two dates (30 days per month system)
function calculateDaysBetween(start, end) {
    const startYear = start.getFullYear();
    const startMonth = start.getMonth() + 1; // Convert to 1-12
    const startDay = start.getDate();
    
    const endYear = end.getFullYear();
    const endMonth = end.getMonth() + 1; // Convert to 1-12
    const endDay = end.getDate();
    
    // Ensure days don't exceed 30 in our system
    const adjustedStartDay = Math.min(startDay, 30);
    const adjustedEndDay = Math.min(endDay, 30);
    
    // Calculate total months
    const totalStartMonths = (startYear * 12) + startMonth;
    const totalEndMonths = (endYear * 12) + endMonth;
    
    let totalDays = 0;
    
    if (totalStartMonths === totalEndMonths) {
        // Same month
        totalDays = adjustedEndDay - adjustedStartDay + 1;
    } else {
        // Different months
        // Days remaining in start month
        totalDays += (30 - adjustedStartDay + 1);
        
        // Full months between
        const monthsBetween = totalEndMonths - totalStartMonths - 1;
        totalDays += monthsBetween * 30;
        
        // Days in end month
        totalDays += adjustedEndDay;
    }
    
    return Math.max(0, totalDays);
}

// Display calculation results - Enhanced
function displayResults(result) {
    const resultsCard = document.getElementById('resultsCard');
    const resultsContent = document.getElementById('resultsContent');
    
    let html = '';
    
    // Yearly breakdown if multiple years
    if (result.yearlyCalculations.length > 1) {
        html += '<h6 class="mb-3" style="color: #2c3e50; font-weight: 700;">التفصيل السنوي:</h6>';
        result.yearlyCalculations.forEach(calc => {
            html += `
                <div class="result-item">
                    <div class="result-label">سنة ${calc.year} (${calc.days} يوم)</div>
                    <div class="result-value">${formatCurrency(calc.allowance)}</div>
                    <div class="result-details">
                        الراتب: ${formatCurrency(calc.salary)}
                        ${calc.isCurrentSalaryBased ? ' (من المربوط الحالي)' : ''}
                    </div>
                </div>
            `;
        });
        
        html += '<hr class="my-3" style="border-color: #dee2e6;">';
    }
    
    // Total result
    html += `
        <div class="total-result">
            <div class="total-label">إجمالي البدل</div>
            <div class="total-value">${formatCurrency(result.totalAllowance)}</div>
            <div class="total-details">
                ${result.allowanceInfo.name} - ${result.totalDays} يوم
            </div>
        </div>
    `;
    
    resultsContent.innerHTML = html;
    resultsCard.style.display = 'block';
    
    // Scroll to results
    resultsCard.scrollIntoView({ behavior: 'smooth' });
}

// Display period details
function displayPeriodDetails(periodDetails) {
    const detailsCard = document.getElementById('periodDetails');
    const detailsContent = document.getElementById('periodDetailsContent');
    
    const html = `
        <div class="period-item">
            <span class="period-label">تاريخ البداية</span>
            <span class="period-value">${formatDateArabic(periodDetails.startDate)}</span>
        </div>
        <div class="period-item">
            <span class="period-label">تاريخ النهاية</span>
            <span class="period-value">${formatDateArabic(periodDetails.endDate)}</span>
        </div>
        <div class="period-item">
            <span class="period-label">إجمالي الأيام</span>
            <span class="period-value">${periodDetails.totalDays} يوم</span>
        </div>
        <div class="period-item">
            <span class="period-label">عدد السنوات</span>
            <span class="period-value">${periodDetails.years.length} سنة</span>
        </div>
        <div class="period-item">
            <span class="period-label">طريقة الحساب</span>
            <span class="period-value">30 يوم لكل شهر</span>
        </div>
    `;
    
    detailsContent.innerHTML = html;
    detailsCard.style.display = 'block';
}

// Utility functions
function formatCurrency(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) {
        return '0.00 ر.س';
    }
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatDate(date) {
    if (!date || !(date instanceof Date)) {
        return '';
    }
    return date.toISOString().split('T')[0];
}

function formatDateArabic(date) {
    if (!date || !(date instanceof Date)) {
        return 'غير محدد';
    }
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(date);
}

// Make utility functions globally available
window.formatCurrency = formatCurrency;
window.formatDate = formatDate;
window.formatDateArabic = formatDateArabic;

// Print results
function printResults() {
    window.print();
}

// Export to PDF
function exportToPDF() {
    if (!validateFormForReport()) {
        return;
    }

    // Validate form function
    function validateFormForReport() {
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');
        const currentSalary = document.getElementById('currentSalary');
        
        if (!startDate || !endDate || !currentSalary) {
            alert('يرجى التأكد من تعبئة جميع الحقول');
            return false;
        }
        
        if (!startDate.value || !endDate.value || !currentSalary.value) {
            alert('يرجى تعبئة جميع الحقول المطلوبة');
            return false;
        }
        
        return true;
    }
    
    if (typeof jsPDF === 'undefined') {
        alert('مكتبة PDF غير متوفرة');
        return;
    }
    
    if (!currentAllowance) {
        alert('لم يتم تحديد نوع البدل');
        return;
    }
    
    const allowanceSelect = document.getElementById('allowanceSelect');
    const selectedOption = allowanceSelect ? allowanceSelect.querySelector(`option[value="${currentAllowance.id}"]`) : null;
    const allowanceName = selectedOption ? selectedOption.textContent : currentAllowance.name;
    const allowanceDescription = selectedOption ? selectedOption.getAttribute('data-description') : currentAllowance.description;
    const allowanceType = selectedOption ? selectedOption.getAttribute('data-type') : currentAllowance.type;
    const allowanceValue = selectedOption ? selectedOption.getAttribute('data-value') : currentAllowance.value;
    const allowanceIcon = selectedOption ? selectedOption.getAttribute('data-icon') : currentAllowance.icon;

    // Update allowance header
    const allowanceHeader = document.getElementById('allowanceHeader');
    if (allowanceHeader) {
        allowanceHeader.innerHTML = `
            <div class="allowance-icon-large">
                <i class="${allowanceIcon || 'fas fa-calculator'}"></i>
            </div>
            <h2>${allowanceName}</h2>
            <p class="lead mb-3">${allowanceDescription}</p>
            <div class="row">
                <div class="col-md-6">
                    <div class="text-center">
                        <small>نوع الحساب</small>
                        <div class="h5">${allowanceType === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت'}</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="text-center">
                        <small>القيمة</small>
                        <div class="h5">${allowanceType === 'percentage' ? `${allowanceValue}%` : `${allowanceValue} ريال/يوم`}</div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // Generate PDF
    try {
        const { jsPDF } = window.jspdf;
        const doc = new jsPDF();
        
        doc.setFont('helvetica');
        doc.setFontSize(16);
        
        doc.text('تقرير حساب البدل', 105, 20, { align: 'center' });
        
        doc.save('تقرير-البدل.pdf');
    } catch (error) {
        console.error('PDF export error:', error);
        alert('حدث خطأ في تصدير PDF: ' + error.message);
    }
}

// Test function for date calculations (for debugging)
function testDateCalculations() {
    console.log('Testing 30-day month system:');
    
    // Test 1: Full year should be 360 days
    const jan1 = new Date(2024, 0, 1);
    const dec30 = new Date(2024, 11, 30);
    console.log(`Full year 2024: ${calculateDaysBetween(jan1, dec30)} days (should be 360)`);
    
    // Test 2: Full month should be 30 days
    const month1 = new Date(2024, 0, 1);
    const month30 = new Date(2024, 0, 30);
    console.log(`Full month January 2024: ${calculateDaysBetween(month1, month30)} days (should be 30)`);
    
    // Test 3: Multiple years
    const jan1_2023 = new Date(2023, 0, 1);
    const dec30_2024 = new Date(2024, 11, 30);
    console.log(`Multiple years (2023-2024): ${calculateDaysBetween(jan1_2023, dec30_2024)} days (should be 720)`);
}

// Select allowance and update form
function selectAllowance(allowanceId) {
    currentAllowance = allowances.find(a => a.id === allowanceId);
    
    if (!currentAllowance) {
        console.error('Allowance not found:', allowanceId);
        return;
    }
    
    console.log('Selected allowance:', currentAllowance);
    
    // Update UI
    updateAllowanceDisplay();
    
    // Check if we need yearly salary fields
    setTimeout(() => {
        checkAndShowYearlySalaryFields();
    }, 100);
    
    // Auto calculate if form is valid
    if (isFormValid()) {
        calculateAllowance();
    }
}

// Update allowance display
function updateAllowanceDisplay() {
    const allowanceSelect = document.getElementById('allowanceSelect');
    const selectedOption = allowanceSelect.querySelector(`option[value="${currentAllowance.id}"]`);
    const allowanceName = selectedOption.textContent;
    const allowanceDescription = selectedOption.getAttribute('data-description');
    const allowanceType = selectedOption.getAttribute('data-type');
    const allowanceValue = selectedOption.getAttribute('data-value');
    const allowanceIcon = selectedOption.getAttribute('data-icon');

    // Update allowance header
    const allowanceHeader = document.getElementById('allowanceHeader');
    allowanceHeader.innerHTML = `
        <div class="allowance-icon-large">
            <i class="${allowanceIcon}"></i>
        </div>
        <h2>${allowanceName}</h2>
        <p class="lead mb-3">${allowanceDescription}</p>
        <div class="row">
            <div class="col-md-6">
                <div class="text-center">
                    <small>نوع الحساب</small>
                    <div class="h5">${allowanceType === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت'}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="text-center">
                    <small>القيمة</small>
                    <div class="h5">${allowanceType === 'percentage' ? `${allowanceValue}%` : `${allowanceValue} ريال/يوم`}</div>
                </div>
            </div>
        </div>
    `;
}

// Setup event listeners
function setupEventListeners() {
    console.log('Setting up event listeners...');
    
    // Auto-calculate when inputs change
    const inputs = ['startDate', 'endDate', 'currentSalary', 'previousSalary'];
    inputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', handleInputChange);
            element.addEventListener('input', handleInputChange);
            console.log(`Added listeners to ${id}`);
        }
    });
    
    // Add button event listeners using IDs
    const calculateBtn = document.getElementById('calculateBtn');
    if (calculateBtn) {
        calculateBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Calculate button clicked');
            calculateAllowance();
        });
        console.log('Added calculate button listener');
    }
    
    const testBtn = document.getElementById('testBtn');
    if (testBtn) {
        testBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Test button clicked');
            testYearlyFields();
        });
        console.log('Added test button listener');
    }
    
    // Set default dates
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    
    if (startDateEl) startDateEl.value = formatDate(firstDayOfMonth);
    if (endDateEl) endDateEl.value = formatDate(today);
    
    console.log('Event listeners setup complete');
}

// Handle input changes
function handleInputChange() {
    // Check if we need to show yearly salary fields
    checkAndShowYearlySalaryFields();
    
    // Auto calculate with debounce
    clearTimeout(window.calculateTimeout);
    window.calculateTimeout = setTimeout(() => {
        if (isFormValid()) {
            calculateAllowance();
        }
    }, 500);
}

// Check if we need yearly salary fields
function checkAndShowYearlySalaryFields() {
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    
    if (!startDateEl || !endDateEl || !currentAllowance) return;
    
    const startDate = startDateEl.value;
    const endDate = endDateEl.value;
    
    if (!startDate || !endDate) return;
    
    const isCurrentSalaryBased = currentAllowance.description === 'من المربوط الحالي';
    const yearlySalariesContainer = document.getElementById('yearlySalariesContainer');
    const previousSalaryField = document.getElementById('previousSalaryField');
    
    console.log('Checking yearly fields:', {
        isCurrentSalaryBased,
        startDate,
        endDate,
        hasYearlyFields: !!yearlySalariesContainer
    });
    
    if (isCurrentSalaryBased) {
        const startYear = new Date(startDate).getFullYear();
        const endYear = new Date(endDate).getFullYear();
        const yearsDiff = endYear - startYear + 1;
        
        console.log('Years difference:', yearsDiff);
        
        if (yearsDiff > 1) {
            // Multiple years - show yearly salary fields
            const years = [];
            for (let year = startYear; year <= endYear; year++) {
                years.push(year);
            }
            
            generateYearlySalaryFields(years);
            if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'block';
            if (previousSalaryField) previousSalaryField.style.display = 'none';
        } else {
            // Single year - hide yearly fields
            if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'none';
            if (previousSalaryField) previousSalaryField.style.display = 'block';
        }
    } else {
        // Normal calculation - show previous salary field
        if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'none';
        if (previousSalaryField) previousSalaryField.style.display = 'block';
    }
}

// Generate yearly salary fields
function generateYearlySalaryFields(years) {
    const container = document.getElementById('yearlySalariesFields');
    if (!container) {
        console.error('yearlySalariesFields container not found');
        return;
    }
    
    console.log('Generating fields for years:', years);
    
    // Check if fields already exist for these years
    const existingFields = container.querySelectorAll('.yearly-salary-field');
    const existingYears = Array.from(existingFields).map(field => {
        const input = field.querySelector('.yearly-salary-input');
        return input ? input.dataset.year : null;
    }).filter(year => year !== null);
    
    // Only generate if years are different
    if (JSON.stringify(existingYears.sort()) === JSON.stringify(years.map(String).sort())) {
        console.log('Fields already exist for these years');
        return;
    }
    
    // Clear existing fields
    container.innerHTML = '';
    
    // Generate new fields
    years.forEach(year => {
        const fieldDiv = document.createElement('div');
        fieldDiv.className = 'yearly-salary-field mb-3';
        fieldDiv.innerHTML = `
            <label for="salary_${year}" class="form-label">راتب سنة ${year}</label>
            <div class="input-group">
                <input type="number" 
                       class="form-control yearly-salary-input" 
                       id="salary_${year}" 
                       data-year="${year}"
                       placeholder="أدخل الراتب لسنة ${year}" 
                       min="0" 
                       step="0.01">
                <span class="input-group-text">ر.س</span>
            </div>
        `;
        container.appendChild(fieldDiv);
        
        // Add event listener for auto-calculation
        const input = fieldDiv.querySelector('.yearly-salary-input');
        if (input) {
            input.addEventListener('input', handleInputChange);
            input.addEventListener('change', handleInputChange);
        }
    });
    
    console.log('Generated yearly salary fields for years:', years);
}

// Auto calculate with debounce
function autoCalculate() {
    clearTimeout(calculateTimeout);
    calculateTimeout = setTimeout(() => {
        if (isFormValid()) {
            calculateAllowance();
        }
    }, 500);
}

// Check if form is valid
function isFormValid() {
    const startDate = document.getElementById('startDate')?.value;
    const endDate = document.getElementById('endDate')?.value;
    const currentSalary = document.getElementById('currentSalary')?.value;
    
    console.log('Validating form:', { startDate, endDate, currentSalary });
    
    if (!startDate || !endDate || !currentSalary || parseFloat(currentSalary) <= 0) {
        console.log('Basic validation failed');
        return false;
    }
    
    // Check yearly salary fields if they are visible
    const isCurrentSalaryBased = currentAllowance && currentAllowance.description === 'من المربوط الحالي';
    const yearlySalariesContainer = document.getElementById('yearlySalariesContainer');
    
    if (isCurrentSalaryBased && yearlySalariesContainer && yearlySalariesContainer.style.display !== 'none') {
        const yearlyInputs = document.querySelectorAll('.yearly-salary-input');
        for (let input of yearlyInputs) {
            const value = parseFloat(input.value);
            if (!input.value || value <= 0) {
                console.log(`Validation failed for year ${input.dataset.year}`);
                return false;
            }
        }
    }
    
    return true;
}

// Calculate allowance
function calculateAllowance() {
    if (!isFormValid()) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // Show calculating state
    document.body.classList.add('calculating');
    
    setTimeout(() => {
        try {
            const result = performCalculation();
            displayResults(result);
            displayPeriodDetails(result.periodDetails);
        } catch (error) {
            alert('حدث خطأ في الحساب: ' + error.message);
        } finally {
            document.body.classList.remove('calculating');
        }
    }, 500);
}

// Perform the actual calculation
function performCalculation() {
    const startDateElement = document.getElementById('startDate');
    const endDateElement = document.getElementById('endDate');
    const currentSalaryElement = document.getElementById('currentSalary');
    const previousSalaryElement = document.getElementById('previousSalary');

    if (!startDateElement || !endDateElement || !currentSalaryElement) {
        throw new Error('عنصر من عناصر النموذج غير موجود');
    }

    const startDate = new Date(startDateElement.value);
    const endDate = new Date(endDateElement.value);
    const currentSalary = parseFloat(currentSalaryElement.value);
    const previousSalary = previousSalaryElement ? parseFloat(previousSalaryElement.value) || currentSalary : currentSalary;
    
    
    // Validate dates
    if (endDate <= startDate) {
        throw new Error('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
    }
    
    // Calculate period details
    const periodDetails = calculatePeriodDetails(startDate, endDate);
    
    // Check if this is "من المربوط الحالي" calculation
    const isCurrentSalaryBased = currentAllowance.description === 'من المربوط الحالي';
    
    // Get yearly salaries if needed
    const yearlySalaries = {};
    if (isCurrentSalaryBased && periodDetails.years.length > 1) {
        // Collect yearly salaries from dynamic fields
        let hasEmptyFields = false;
        
        periodDetails.years.forEach(yearData => {
            const salaryInput = document.getElementById(`salary_${yearData.year}`);
            if (salaryInput) {
                const salary = parseFloat(salaryInput.value);
                if (!salary || salary <= 0) {
                    hasEmptyFields = true;
                } else {
                    yearlySalaries[yearData.year] = salary;
                }
            }
        });
        
        if (hasEmptyFields) {
            throw new Error('يرجى إدخال راتب صحيح لجميع السنوات');
        }
    }
    
    // Calculate allowance for each year
    let totalAllowance = 0;
    const yearlyCalculations = [];
    
    periodDetails.years.forEach(yearData => {
        let salary;
        
        if (isCurrentSalaryBased && periodDetails.years.length > 1) {
            // Use yearly salaries from dynamic fields
            salary = yearlySalaries[yearData.year];
            if (!salary) {
                throw new Error(`لم يتم العثور على راتب سنة ${yearData.year}`);
            }
        } else if (isCurrentSalaryBased) {
            // Single year - use current salary
            salary = currentSalary;
        } else {
            // Normal calculation - use current salary for current year, previous for others
            const currentYear = new Date().getFullYear();
            salary = yearData.year === currentYear ? currentSalary : previousSalary;
        }
        
        let yearlyAllowance = 0;
        
        if (currentAllowance.type === 'percentage') {
            // Percentage calculation: salary × percentage ÷ 100 × days ÷ 365
            yearlyAllowance = (salary * currentAllowance.value / 100) * (yearData.days / 365);
        } else if (currentAllowance.type === 'fixed') {
            // Fixed amount calculation: fixed amount × days ÷ 365
            yearlyAllowance = currentAllowance.value * (yearData.days / 365);
        }
        
        totalAllowance += yearlyAllowance;
        
        yearlyCalculations.push({
            year: yearData.year,
            salary: salary,
            days: yearData.days,
            allowance: yearlyAllowance,
            percentage: currentAllowance.type === 'percentage' ? currentAllowance.value : null
        });
    });
    
    return {
        totalAllowance: totalAllowance,
        yearlyCalculations: yearlyCalculations,
        periodDetails: periodDetails,
        allowanceInfo: currentAllowance
    };
}

// Calculate period details (30 days per month, 360 days per year)
function calculatePeriodDetails(startDate, endDate) {
    const startYear = startDate.getFullYear();
    const startMonth = startDate.getMonth() + 1;
    const startDay = startDate.getDate();
    
    const endYear = endDate.getFullYear();
    const endMonth = endDate.getMonth() + 1;
    const endDay = endDate.getDate();
    
    // Calculate total days using 30-day month system
    const totalDays = calculateDaysBetween(startDate, endDate);
    
    // Get years in the period
    const years = [];
    for (let year = startYear; year <= endYear; year++) {
        const yearStartDate = year === startYear ? startDate : new Date(year, 0, 1);
        const yearEndDate = year === endYear ? endDate : new Date(year, 11, 30);
        
        const yearDays = calculateDaysBetween(yearStartDate, yearEndDate);
        
        years.push({
            year: year,
            days: yearDays,
            startDate: yearStartDate,
            endDate: yearEndDate
        });
    }
    
    return {
        totalDays: totalDays,
        years: years,
        startDate: startDate,
        endDate: endDate
    };
}

// Calculate days between two dates (30 days per month system)
function calculateDaysBetween(start, end) {
    const startYear = start.getFullYear();
    const startMonth = start.getMonth() + 1; // Convert to 1-12
    const startDay = start.getDate();
    
    const endYear = end.getFullYear();
    const endMonth = end.getMonth() + 1; // Convert to 1-12
    const endDay = end.getDate();
    
    // Ensure days don't exceed 30 in our system
    const adjustedStartDay = Math.min(startDay, 30);
    const adjustedEndDay = Math.min(endDay, 30);
    
    // Calculate total months
    const totalStartMonths = (startYear * 12) + startMonth;
    const totalEndMonths = (endYear * 12) + endMonth;
    
    let totalDays = 0;
    
    if (totalStartMonths === totalEndMonths) {
        // Same month
        totalDays = adjustedEndDay - adjustedStartDay + 1;
    } else {
        // Different months
        // Days remaining in start month
        totalDays += (30 - adjustedStartDay + 1);
        
        // Full months between
        const monthsBetween = totalEndMonths - totalStartMonths - 1;
        totalDays += monthsBetween * 30;
        
        // Days in end month
        totalDays += adjustedEndDay;
    }
    
    return Math.max(0, totalDays);
}

// Display calculation results
function displayResults(result) {
    const resultsCard = document.getElementById('resultsCard');
    const resultsContent = document.getElementById('resultsContent');
    
    let html = '';
    
    // Yearly breakdown if multiple years
    if (result.yearlyCalculations.length > 1) {
        html += '<h6 class="mb-3">التفصيل السنوي:</h6>';
        result.yearlyCalculations.forEach(calc => {
            html += `
                <div class="result-item">
                    <div class="result-label">سنة ${calc.year} (${calc.days} يوم)</div>
                    <div class="result-value">${formatCurrency(calc.allowance)}</div>
                    <div class="result-details">
                        الراتب: ${formatCurrency(calc.salary)}
                        ${calc.isCurrentSalaryBased ? ' (من المربوط الحالي)' : ''}
                    </div>
                </div>
            `;
        });
        
        html += '<hr class="my-3">';
    }
    
    // Total result
    html += `
        <div class="total-result">
            <div class="total-label">إجمالي البدل</div>
            <div class="total-value">${formatCurrency(result.totalAllowance)}</div>
            <div class="total-details">
                ${result.allowanceInfo.name} - ${result.totalDays} يوم
            </div>
        </div>
    `;
    
    resultsContent.innerHTML = html;
    resultsCard.style.display = 'block';
    
    // Scroll to results
    resultsCard.scrollIntoView({ behavior: 'smooth' });
}

// Display period details
function displayPeriodDetails(periodDetails) {
    const detailsCard = document.getElementById('periodDetails');
    const detailsContent = document.getElementById('periodDetailsContent');
    
    const html = `
        <div class="period-item">
            <span class="period-label">تاريخ البداية</span>
            <span class="period-value">${formatDateArabic(periodDetails.startDate)}</span>
        </div>
        <div class="period-item">
            <span class="period-label">تاريخ النهاية</span>
            <span class="period-value">${formatDateArabic(periodDetails.endDate)}</span>
        </div>
        <div class="period-item">
            <span class="period-label">إجمالي الأيام</span>
            <span class="period-value">${periodDetails.totalDays} يوم</span>
        </div>
        <div class="period-item">
            <span class="period-label">عدد السنوات</span>
            <span class="period-value">${periodDetails.years.length} سنة</span>
        </div>
        <div class="period-item">
            <span class="period-label">طريقة الحساب</span>
            <span class="period-value">30 يوم لكل شهر</span>
        </div>
    `;
    
    detailsContent.innerHTML = html;
    detailsCard.style.display = 'block';
}

// Utility functions
function formatCurrency(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) {
        return '0.00 ر.س';
    }
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatDate(date) {
    if (!date || !(date instanceof Date)) {
        return '';
    }
    return date.toISOString().split('T')[0];
}

function formatDateArabic(date) {
    if (!date || !(date instanceof Date)) {
        return 'غير محدد';
    }
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(date);
}

// Make utility functions globally available
window.formatCurrency = formatCurrency;
window.formatDate = formatDate;
window.formatDateArabic = formatDateArabic;

// Print results
function printResults() {
    window.print();
}

// Export to PDF
function exportToPDF() {
    if (!validateFormForReport()) {
        return;
    }

    // Validate form function
    function validateFormForReport() {
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');
        const currentSalary = document.getElementById('currentSalary');
        
        if (!startDate || !endDate || !currentSalary) {
            alert('يرجى التأكد من تعبئة جميع الحقول');
            return false;
        }
        
        if (!startDate.value || !endDate.value || !currentSalary.value) {
            alert('يرجى تعبئة جميع الحقول المطلوبة');
           
            console.log(`Added listeners to ${id}`);
        }
    };
    
    // Add button event listeners using IDs
    const calculateBtn = document.getElementById('calculateBtn');
    if (calculateBtn) {
        calculateBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Calculate button clicked');
            calculateAllowance();
        });
        console.log('Added calculate button listener');
    }
    
    const testBtn = document.getElementById('testBtn');
    if (testBtn) {
        testBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Test button clicked');
            testYearlyFields();
        });
        console.log('Added test button listener');
    }
    
    // Set default dates
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    
    if (startDateEl) startDateEl.value = formatDate(firstDayOfMonth);
    if (endDateEl) endDateEl.value = formatDate(today);
    
    console.log('Event listeners setup complete');
}

// Handle input changes
function handleInputChange() {
    // Check if we need to show yearly salary fields
    checkAndShowYearlySalaryFields();
    
    // Auto calculate with debounce
    clearTimeout(window.calculateTimeout);
    window.calculateTimeout = setTimeout(() => {
        if (isFormValid()) {
            calculateAllowance();
        }
    }, 500);
}

// Check if we need yearly salary fields
function checkAndShowYearlySalaryFields() {
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    
    if (!startDateEl || !endDateEl || !currentAllowance) return;
    
    const startDate = startDateEl.value;
    const endDate = endDateEl.value;
    
    if (!startDate || !endDate) return;
    
    const isCurrentSalaryBased = currentAllowance.description === 'من المربوط الحالي';
    const yearlySalariesContainer = document.getElementById('yearlySalariesContainer');
    const previousSalaryField = document.getElementById('previousSalaryField');
    
    console.log('Checking yearly fields:', {
        isCurrentSalaryBased,
        startDate,
        endDate,
        hasYearlyFields: !!yearlySalariesContainer
    });
    
    if (isCurrentSalaryBased) {
        const startYear = new Date(startDate).getFullYear();
        const endYear = new Date(endDate).getFullYear();
        const yearsDiff = endYear - startYear + 1;
        
        console.log('Years difference:', yearsDiff);
        
        if (yearsDiff > 1) {
            // Multiple years - show yearly salary fields
            const years = [];
            for (let year = startYear; year <= endYear; year++) {
                years.push(year);
            }
            
            generateYearlySalaryFields(years);
            if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'block';
            if (previousSalaryField) previousSalaryField.style.display = 'none';
        } else {
            // Single year - hide yearly fields
            if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'none';
            if (previousSalaryField) previousSalaryField.style.display = 'block';
        }
    } else {
        // Normal calculation - show previous salary field
        if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'none';
        if (previousSalaryField) previousSalaryField.style.display = 'block';
    }
}

// Generate yearly salary fields
function generateYearlySalaryFields(years) {
    const container = document.getElementById('yearlySalariesFields');
    if (!container) {
        console.error('yearlySalariesFields container not found');
        return;
    }
    
    console.log('Generating fields for years:', years);
    
    // Check if fields already exist for these years
    const existingFields = container.querySelectorAll('.yearly-salary-field');
    const existingYears = Array.from(existingFields).map(field => {
        const input = field.querySelector('.yearly-salary-input');
        return input ? input.dataset.year : null;
    }).filter(year => year !== null);
    
    // Only generate if years are different
    if (JSON.stringify(existingYears.sort()) === JSON.stringify(years.map(String).sort())) {
        console.log('Fields already exist for these years');
        return;
    }
    
    // Clear existing fields
    container.innerHTML = '';
    
    // Generate new fields
    years.forEach(year => {
        const fieldDiv = document.createElement('div');
        fieldDiv.className = 'yearly-salary-field mb-3';
        fieldDiv.innerHTML = `
            <label for="salary_${year}" class="form-label">راتب سنة ${year}</label>
            <div class="input-group">
                <input type="number" 
                       class="form-control yearly-salary-input" 
                       id="salary_${year}" 
                       data-year="${year}"
                       placeholder="أدخل الراتب لسنة ${year}" 
                       min="0" 
                       step="0.01">
                <span class="input-group-text">ر.س</span>
            </div>
        `;
        container.appendChild(fieldDiv);
        
        // Add event listener for auto-calculation
        const input = fieldDiv.querySelector('.yearly-salary-input');
        if (input) {
            input.addEventListener('input', handleInputChange);
            input.addEventListener('change', handleInputChange);
        }
    });
    
    console.log('Generated yearly salary fields for years:', years);
}

// Auto calculate with debounce
function autoCalculate() {
    clearTimeout(calculateTimeout);
    calculateTimeout = setTimeout(() => {
        if (isFormValid()) {
            calculateAllowance();
        }
    }, 500);
}

// Check if form is valid
function isFormValid() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const currentSalary = document.getElementById('currentSalary').value;
    
    console.log('Validating form:', { startDate, endDate, currentSalary });
    
    if (!startDate || !endDate || !currentSalary || parseFloat(currentSalary) <= 0) {
        console.log('Basic validation failed');
        return false;
    }
    
    // Check yearly salary fields if they are visible
    const isCurrentSalaryBased = currentAllowance && currentAllowance.description === 'من المربوط الحالي';
    const yearlySalariesContainer = document.getElementById('yearlySalariesContainer');
    
    if (isCurrentSalaryBased && yearlySalariesContainer && yearlySalariesContainer.style.display !== 'none') {
        console.log('Checking yearly salary fields...');
        const yearlyInputs = document.querySelectorAll('.yearly-salary-input');
        console.log('Found yearly inputs:', yearlyInputs.length);
        
        for (let input of yearlyInputs) {
            const value = parseFloat(input.value);
            console.log(`Checking input for year ${input.dataset.year}: ${value}`);
            if (!input.value || value <= 0) {
                console.log(`Validation failed for year ${input.dataset.year}`);
                return false;
            }
        }
    }
    
    console.log('Form validation passed');
    return true;
}

// Calculate allowance
function calculateAllowance() {
    if (!isFormValid()) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // Show calculating state
    document.body.classList.add('calculating');
    
    setTimeout(() => {
        try {
            const result = performCalculation();
            displayResults(result);
            displayPeriodDetails(result.periodDetails);
        } catch (error) {
            alert('حدث خطأ في الحساب: ' + error.message);
        } finally {
            document.body.classList.remove('calculating');
        }
    }, 500);
}

// Perform the actual calculation
function performCalculation() {
    const startDate = new Date(document.getElementById('startDate').value);
    const endDate = new Date(document.getElementById('endDate').value);
    const currentSalary = parseFloat(document.getElementById('currentSalary').value);
    const previousSalary = parseFloat(document.getElementById('previousSalary').value) || currentSalary;
    
    // Validate dates
    if (endDate <= startDate) {
        throw new Error('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
    }
    
    // Calculate period details
    const periodDetails = calculatePeriodDetails(startDate, endDate);
    
    // Check if this is "من المربوط الحالي" calculation
    const isCurrentSalaryBased = currentAllowance.description === 'من المربوط الحالي';
    
    // Get yearly salaries if needed
    const yearlySalaries = {};
    if (isCurrentSalaryBased && periodDetails.years.length > 1) {
        // Collect yearly salaries from dynamic fields
        let hasEmptyFields = false;
        
        periodDetails.years.forEach(yearData => {
            const salaryInput = document.getElementById(`salary_${yearData.year}`);
            if (salaryInput) {
                const salary = parseFloat(salaryInput.value);
                if (!salary || salary <= 0) {
                    hasEmptyFields = true;
                } else {
                    yearlySalaries[yearData.year] = salary;
                }
            }
        });
        
        if (hasEmptyFields) {
            throw new Error('يرجى إدخال راتب صحيح لجميع السنوات');
        }
    }
    
    // Calculate allowance for each year
    let totalAllowance = 0;
    const yearlyCalculations = [];
    
    periodDetails.years.forEach(yearData => {
        let salary;
        
        if (isCurrentSalaryBased && periodDetails.years.length > 1) {
            // Use yearly salaries from dynamic fields
            salary = yearlySalaries[yearData.year];
            if (!salary) {
                throw new Error(`لم يتم العثور على راتب سنة ${yearData.year}`);
            }
        } else if (isCurrentSalaryBased) {
            // Single year - use current salary
            salary = currentSalary;
        } else {
            // Normal calculation - use current salary for current year, previous for others
            const currentYear = new Date().getFullYear();
            salary = yearData.year === currentYear ? currentSalary : previousSalary;
        }
        
        let yearlyAllowance = 0;
        
        if (currentAllowance.type === 'percentage') {
            // Percentage calculation: salary × percentage ÷ 100 × days ÷ 365
            yearlyAllowance = (salary * currentAllowance.value / 100) * (yearData.days / 365);
        } else if (currentAllowance.type === 'fixed') {
            // Fixed amount calculation: fixed amount × days ÷ 365
            yearlyAllowance = currentAllowance.value * (yearData.days / 365);
        }
        
        totalAllowance += yearlyAllowance;
        
        yearlyCalculations.push({
            year: yearData.year,
            salary: salary,
            days: yearData.days,
            allowance: yearlyAllowance,
            percentage: currentAllowance.type === 'percentage' ? currentAllowance.value : null
        });
    });
    
    return {
        totalAllowance: totalAllowance,
        yearlyCalculations: yearlyCalculations,
        periodDetails: periodDetails,
        allowanceInfo: currentAllowance
    };
}

// Calculate period details (30 days per month, 360 days per year)
function calculatePeriodDetails(startDate, endDate) {
    const startYear = startDate.getFullYear();
    const startMonth = startDate.getMonth() + 1;
    const startDay = startDate.getDate();
    
    const endYear = endDate.getFullYear();
    const endMonth = endDate.getMonth() + 1;
    const endDay = endDate.getDate();
    
    // Calculate total days using 30-day month system
    const totalDays = calculateDaysBetween(startDate, endDate);
    
    // Get years in the period
    const years = [];
    for (let year = startYear; year <= endYear; year++) {
        const yearStartDate = year === startYear ? startDate : new Date(year, 0, 1);
        const yearEndDate = year === endYear ? endDate : new Date(year, 11, 30);
        
        const yearDays = calculateDaysBetween(yearStartDate, yearEndDate);
        
        years.push({
            year: year,
            days: yearDays,
            startDate: yearStartDate,
            endDate: yearEndDate
        });
    }
    
    return {
        totalDays: totalDays,
        years: years,
        startDate: startDate,
        endDate: endDate
    };
}

// Calculate days between two dates (30 days per month system)
function calculateDaysBetween(start, end) {
    const startYear = start.getFullYear();
    const startMonth = start.getMonth() + 1; // Convert to 1-12
    const startDay = start.getDate();
    
    const endYear = end.getFullYear();
    const endMonth = end.getMonth() + 1; // Convert to 1-12
    const endDay = end.getDate();
    
    // Ensure days don't exceed 30 in our system
    const adjustedStartDay = Math.min(startDay, 30);
    const adjustedEndDay = Math.min(endDay, 30);
    
    // Calculate total months
    const totalStartMonths = (startYear * 12) + startMonth;
    const totalEndMonths = (endYear * 12) + endMonth;
    
    let totalDays = 0;
    
    if (totalStartMonths === totalEndMonths) {
        // Same month
        totalDays = adjustedEndDay - adjustedStartDay + 1;
    } else {
        // Different months
        // Days remaining in start month
        totalDays += (30 - adjustedStartDay + 1);
        
        // Full months between
        const monthsBetween = totalEndMonths - totalStartMonths - 1;
        totalDays += monthsBetween * 30;
        
        // Days in end month
        totalDays += adjustedEndDay;
    }
    
    return Math.max(0, totalDays);
}

// Display calculation results
function displayResults(result) {
    const resultsCard = document.getElementById('resultsCard');
    const resultsContent = document.getElementById('resultsContent');
    
    let html = '';
    
    // Yearly breakdown if multiple years
    if (result.yearlyCalculations.length > 1) {
        html += '<h6 class="mb-3">التفصيل السنوي:</h6>';
        result.yearlyCalculations.forEach(calc => {
            html += `
                <div class="result-item">
                    <div class="result-label">سنة ${calc.year} (${calc.days} يوم)</div>
                    <div class="result-value">${formatCurrency(calc.allowance)}</div>
                    <div class="result-details">
                        الراتب: ${formatCurrency(calc.salary)}
                        ${calc.isCurrentSalaryBased ? ' (من المربوط الحالي)' : ''}
                    </div>
                </div>
            `;
        });
        
        html += '<hr class="my-3">';
    }
    
    // Total result
    html += `
        <div class="total-result">
            <div class="total-label">إجمالي البدل</div>
            <div class="total-value">${formatCurrency(result.totalAllowance)}</div>
            <div class="total-details">
                ${result.allowanceInfo.name} - ${result.totalDays} يوم
            </div>
        </div>
    `;
    
    resultsContent.innerHTML = html;
    resultsCard.style.display = 'block';
    
    // Scroll to results
    resultsCard.scrollIntoView({ behavior: 'smooth' });
}

// Display period details
function displayPeriodDetails(periodDetails) {
    const detailsCard = document.getElementById('periodDetails');
    const detailsContent = document.getElementById('periodDetailsContent');
    
    const html = `
        <div class="period-item">
            <span class="period-label">تاريخ البداية</span>
            <span class="period-value">${formatDateArabic(periodDetails.startDate)}</span>
        </div>
        <div class="period-item">
            <span class="period-label">تاريخ النهاية</span>
            <span class="period-value">${formatDateArabic(periodDetails.endDate)}</span>
        </div>
        <div class="period-item">
            <span class="period-label">إجمالي الأيام</span>
            <span class="period-value">${periodDetails.totalDays} يوم</span>
        </div>
        <div class="period-item">
            <span class="period-label">عدد السنوات</span>
            <span class="period-value">${periodDetails.years.length} سنة</span>
        </div>
        <div class="period-item">
            <span class="period-label">طريقة الحساب</span>
            <span class="period-value">30 يوم لكل شهر</span>
        </div>
    `;
    
    detailsContent.innerHTML = html;
    detailsCard.style.display = 'block';
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatDate(date) {
    return date.toISOString().split('T')[0];
}

function formatDateArabic(date) {
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(date);
}

// Print results
function printResults() {
    window.print();
}

// Export to PDF
function exportToPDF() {
    if (typeof jsPDF === 'undefined') {
        alert('مكتبة PDF غير متوفرة');
        return;
    }
    
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();
    
    doc.setFont('helvetica');
    doc.setFontSize(16);
    
    doc.text('تقرير حساب بدل', 105, 20, { align: 'center' });
    
    doc.save('تقرير-بدل.pdf');
}

// Test function for date calculations (for debugging)
function testDateCalculations() {
    console.log('Testing 30-day month system:');
    
    // Test 1: Full year should be 360 days
    const jan1 = new Date(2024, 0, 1);
    const dec30 = new Date(2024, 11, 30);
    console.log(`Full year 2024: ${calculateDaysBetween(jan1, dec30)} days (should be 360)`);
    
    // Test 2: Full month should be 30 days
    const month1 = new Date(2024, 0, 1);
    const month30 = new Date(2024, 0, 30);
    console.log(`Full month January 2024: ${calculateDaysBetween(month1, month30)} days (should be 30)`);
    
    // Test 3: Multiple years
    const jan1_2023 = new Date(2023, 0, 1);
    const dec30_2024 = new Date(2024, 11, 30);
    console.log(`Multiple years (2023-2024): ${calculateDaysBetween(jan1_2023, dec30_2024)} days (should be 720)`);
}

// Select allowance and update form
function selectAllowance(allowanceId) {
    currentAllowance = allowances.find(a => a.id === allowanceId);
    
    if (!currentAllowance) {
        console.error('Allowance not found:', allowanceId);
        return;
    }
    
    console.log('Selected allowance:', currentAllowance);
    
    // Update UI
    updateAllowanceDisplay();
    
    // Check if we need yearly salary fields
    setTimeout(() => {
        checkAndShowYearlySalaryFields();
    }, 100);
    
    // Auto calculate if form is valid
    if (isFormValid()) {
        calculateAllowance();
    }
}

// Update allowance display
function updateAllowanceDisplay() {
    const allowanceSelect = document.getElementById('allowanceSelect');
    const selectedOption = allowanceSelect.querySelector(`option[value="${currentAllowance.id}"]`);
    const allowanceName = selectedOption.textContent;
    const allowanceDescription = selectedOption.getAttribute('data-description');
    const allowanceType = selectedOption.getAttribute('data-type');
    const allowanceValue = selectedOption.getAttribute('data-value');
    const allowanceIcon = selectedOption.getAttribute('data-icon');

    // Update allowance header
    const allowanceHeader = document.getElementById('allowanceHeader');
    allowanceHeader.innerHTML = `
        <div class="allowance-icon-large">
            <i class="${allowanceIcon}"></i>
        </div>
        <h2>${allowanceName}</h2>
        <p class="lead mb-3">${allowanceDescription}</p>
        <div class="row">
            <div class="col-md-6">
                <div class="text-center">
                    <small>نوع الحساب</small>
                    <div class="h5">${allowanceType === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت'}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="text-center">
                    <small>القيمة</small>
                    <div class="h5">${allowanceType === 'percentage' ? `${allowanceValue}%` : `${allowanceValue} ريال/يوم`}</div>
                </div>
            </div>
        </div>
    `;
}

// Setup event listeners
function setupEventListeners() {
    console.log('Setting up event listeners...');
    
    // Auto-calculate when inputs change
    const inputs = ['startDate', 'endDate', 'currentSalary', 'previousSalary'];
    inputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', handleInputChange);
            element.addEventListener('input', handleInputChange);
            console.log(`Added listeners to ${id}`);
        }
    });
    
    // Add button event listeners using IDs
    const calculateBtn = document.getElementById('calculateBtn');
    if (calculateBtn) {
        calculateBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Calculate button clicked');
            calculateAllowance();
        });
        console.log('Added calculate button listener');
    }
    
    const testBtn = document.getElementById('testBtn');
    if (testBtn) {
        testBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Test button clicked');
            testYearlyFields();
        });
        console.log('Added test button listener');
    }
    
    // Set default dates
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    
    if (startDateEl) startDateEl.value = formatDate(firstDayOfMonth);
    if (endDateEl) endDateEl.value = formatDate(today);
    
    console.log('Event listeners setup complete');
}

// Handle input changes
function handleInputChange() {
    // Check if we need to show yearly salary fields
    checkAndShowYearlySalaryFields();
    
    // Auto calculate with debounce
    clearTimeout(window.calculateTimeout);
    window.calculateTimeout = setTimeout(() => {
        if (isFormValid()) {
            calculateAllowance();
        }
    }, 500);
}

// Check if we need yearly salary fields
function checkAndShowYearlySalaryFields() {
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    
    if (!startDateEl || !endDateEl || !currentAllowance) return;
    
    const startDate = startDateEl.value;
    const endDate = endDateEl.value;
    
    if (!startDate || !endDate) return;
    
    const isCurrentSalaryBased = currentAllowance.description === 'من المربوط الحالي';
    const yearlySalariesContainer = document.getElementById('yearlySalariesContainer');
    const previousSalaryField = document.getElementById('previousSalaryField');
    
    console.log('Checking yearly fields:', {
        isCurrentSalaryBased,
        startDate,
        endDate,
        hasYearlyFields: !!yearlySalariesContainer
    });
    
    if (isCurrentSalaryBased) {
        const startYear = new Date(startDate).getFullYear();
        const endYear = new Date(endDate).getFullYear();
        const yearsDiff = endYear - startYear + 1;
        
        console.log('Years difference:', yearsDiff);
        
        if (yearsDiff > 1) {
            // Multiple years - show yearly salary fields
            const years = [];
            for (let year = startYear; year <= endYear; year++) {
                years.push(year);
            }
            
            generateYearlySalaryFields(years);
            if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'block';
            if (previousSalaryField) previousSalaryField.style.display = 'none';
        } else {
            // Single year - hide yearly fields
            if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'none';
            if (previousSalaryField) previousSalaryField.style.display = 'block';
        }
    } else {
        // Normal calculation - show previous salary field
        if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'none';
        if (previousSalaryField) previousSalaryField.style.display = 'block';
    }
}

// Generate yearly salary fields
function generateYearlySalaryFields(years) {
    const container = document.getElementById('yearlySalariesFields');
    if (!container) {
        console.error('yearlySalariesFields container not found');
        return;
    }
    
    console.log('Generating fields for years:', years);
    
    // Check if fields already exist for these years
    const existingFields = container.querySelectorAll('.yearly-salary-field');
    const existingYears = Array.from(existingFields).map(field => {
        const input = field.querySelector('.yearly-salary-input');
        return input ? input.dataset.year : null;
    }).filter(year => year !== null);
    
    // Only generate if years are different
    if (JSON.stringify(existingYears.sort()) === JSON.stringify(years.map(String).sort())) {
        console.log('Fields already exist for these years');
        return;
    }
    
    // Clear existing fields
    container.innerHTML = '';
    
    // Generate new fields
    years.forEach(year => {
        const fieldDiv = document.createElement('div');
        fieldDiv.className = 'yearly-salary-field mb-3';
        fieldDiv.innerHTML = `
            <label for="salary_${year}" class="form-label">راتب سنة ${year}</label>
            <div class="input-group">
                <input type="number" 
                       class="form-control yearly-salary-input" 
                       id="salary_${year}" 
                       data-year="${year}"
                       placeholder="أدخل الراتب لسنة ${year}" 
                       min="0" 
                       step="0.01">
                <span class="input-group-text">ر.س</span>
            </div>
        `;
        container.appendChild(fieldDiv);
        
        // Add event listener for auto-calculation
        const input = fieldDiv.querySelector('.yearly-salary-input');
        if (input) {
            input.addEventListener('input', handleInputChange);
            input.addEventListener('change', handleInputChange);
        }
    });
    
    console.log('Generated yearly salary fields for years:', years);
}

// Auto calculate with debounce
function autoCalculate() {
    clearTimeout(calculateTimeout);
    calculateTimeout = setTimeout(() => {
        if (isFormValid()) {
            calculateAllowance();
        }
    }, 500);
}

// Check if form is valid
function isFormValid() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const currentSalary = document.getElementById('currentSalary').value;
    
    console.log('Validating form:', { startDate, endDate, currentSalary });
    
    if (!startDate || !endDate || !currentSalary || parseFloat(currentSalary) <= 0) {
        console.log('Basic validation failed');
        return false;
    }
    
    // Check yearly salary fields if they are visible
    const isCurrentSalaryBased = currentAllowance && currentAllowance.description === 'من المربوط الحالي';
    const yearlySalariesContainer = document.getElementById('yearlySalariesContainer');
    
    if (isCurrentSalaryBased && yearlySalariesContainer && yearlySalariesContainer.style.display !== 'none') {
        console.log('Checking yearly salary fields...');
        const yearlyInputs = document.querySelectorAll('.yearly-salary-input');
        console.log('Found yearly inputs:', yearlyInputs.length);
        
        for (let input of yearlyInputs) {
            const value = parseFloat(input.value);
            console.log(`Checking input for year ${input.dataset.year}: ${value}`);
            if (!input.value || value <= 0) {
                console.log(`Validation failed for year ${input.dataset.year}`);
                return false;
            }
        }
    }
    
    console.log('Form validation passed');
    return true;
}

// Calculate allowance
function calculateAllowance() {
    if (!isFormValid()) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // Show calculating state
    document.body.classList.add('calculating');
    
    setTimeout(() => {
        try {
            const result = performCalculation();
            displayResults(result);
            displayPeriodDetails(result.periodDetails);
        } catch (error) {
            alert('حدث خطأ في الحساب: ' + error.message);
        } finally {
            document.body.classList.remove('calculating');
        }
    }, 500);
}

// Perform the actual calculation
function performCalculation() {
    const startDate = new Date(document.getElementById('startDate').value);
    const endDate = new Date(document.getElementById('endDate').value);
    const currentSalary = parseFloat(document.getElementById('currentSalary').value);
    const previousSalary = parseFloat(document.getElementById('previousSalary').value) || currentSalary;
    
    // Validate dates
    if (endDate <= startDate) {
        throw new Error('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
    }
    
    // Calculate period details
    const periodDetails = calculatePeriodDetails(startDate, endDate);
    
    // Check if this is "من المربوط الحالي" calculation
    const isCurrentSalaryBased = currentAllowance.description === 'من المربوط الحالي';
    
    // Get yearly salaries if needed
    const yearlySalaries = {};
    if (isCurrentSalaryBased && periodDetails.years.length > 1) {
        // Collect yearly salaries from dynamic fields
        let hasEmptyFields = false;
        
        periodDetails.years.forEach(yearData => {
            const salaryInput = document.getElementById(`salary_${yearData.year}`);
            if (salaryInput) {
                const salary = parseFloat(salaryInput.value);
                if (!salary || salary <= 0) {
                    hasEmptyFields = true;
                } else {
                    yearlySalaries[yearData.year] = salary;
                }
            }
        });
        
        if (hasEmptyFields) {
            throw new Error('يرجى إدخال راتب صحيح لجميع السنوات');
        }
    }
    
    // Calculate allowance for each year
    let totalAllowance = 0;
    const yearlyCalculations = [];
    
    periodDetails.years.forEach(yearData => {
        let salary;
        
        if (isCurrentSalaryBased && periodDetails.years.length > 1) {
            // Use yearly salaries from dynamic fields
            salary = yearlySalaries[yearData.year];
            if (!salary) {
                throw new Error(`لم يتم العثور على راتب سنة ${yearData.year}`);
            }
        } else if (isCurrentSalaryBased) {
            // Single year - use current salary
            salary = currentSalary;
        } else {
            // Normal calculation - use current salary for current year, previous for others
            const currentYear = new Date().getFullYear();
            salary = yearData.year === currentYear ? currentSalary : previousSalary;
        }
        
        let yearlyAllowance = 0;
        
        if (currentAllowance.type === 'percentage') {
            // Percentage calculation: salary × percentage ÷ 100 × days ÷ 365
            yearlyAllowance = (salary * currentAllowance.value / 100) * (yearData.days / 365);
        } else if (currentAllowance.type === 'fixed') {
            // Fixed amount calculation: fixed amount × days ÷ 365
            yearlyAllowance = currentAllowance.value * (yearData.days / 365);
        }
        
        totalAllowance += yearlyAllowance;
        
        yearlyCalculations.push({
            year: yearData.year,
            salary: salary,
            days: yearData.days,
            allowance: yearlyAllowance,
            percentage: currentAllowance.type === 'percentage' ? currentAllowance.value : null
        });
    });
    
    return {
        totalAllowance: totalAllowance,
        yearlyCalculations: yearlyCalculations,
        periodDetails: periodDetails,
        allowanceInfo: currentAllowance
    };
}

// Calculate period details (30 days per month, 360 days per year)
function calculatePeriodDetails(startDate, endDate) {
    const startYear = startDate.getFullYear();
    const startMonth = startDate.getMonth() + 1;
    const startDay = startDate.getDate();
    
    const endYear = endDate.getFullYear();
    const endMonth = endDate.getMonth() + 1;
    const endDay = endDate.getDate();
    
    // Calculate total days using 30-day month system
    const totalDays = calculateDaysBetween(startDate, endDate);
    
    // Get years in the period
    const years = [];
    for (let year = startYear; year <= endYear; year++) {
        const yearStartDate = year === startYear ? startDate : new Date(year, 0, 1);
        const yearEndDate = year === endYear ? endDate : new Date(year, 11, 30);
        
        const yearDays = calculateDaysBetween(yearStartDate, yearEndDate);
        
        years.push({
            year: year,
            days: yearDays,
            startDate: yearStartDate,
            endDate: yearEndDate
        });
    }
    
    return {
        totalDays: totalDays,
        years: years,
        startDate: startDate,
        endDate: endDate
    };
}

// Calculate days between two dates (30 days per month system)
function calculateDaysBetween(start, end) {
    const startYear = start.getFullYear();
    const startMonth = start.getMonth() + 1; // Convert to 1-12
    const startDay = start.getDate();
    
    const endYear = end.getFullYear();
    const endMonth = end.getMonth() + 1; // Convert to 1-12
    const endDay = end.getDate();
    
    // Ensure days don't exceed 30 in our system
    const adjustedStartDay = Math.min(startDay, 30);
    const adjustedEndDay = Math.min(endDay, 30);
    
    // Calculate total months
    const totalStartMonths = (startYear * 12) + startMonth;
    const totalEndMonths = (endYear * 12) + endMonth;
    
    let totalDays = 0;
    
    if (totalStartMonths === totalEndMonths) {
        // Same month
        totalDays = adjustedEndDay - adjustedStartDay + 1;
    } else {
        // Different months
        // Days remaining in start month
        totalDays += (30 - adjustedStartDay + 1);
        
        // Full months between
        const monthsBetween = totalEndMonths - totalStartMonths - 1;
        totalDays += monthsBetween * 30;
        
        // Days in end month
        totalDays += adjustedEndDay;
    }
    
    return Math.max(0, totalDays);
}

// Display calculation results
function displayResults(result) {
    const resultsCard = document.getElementById('resultsCard');
    const resultsContent = document.getElementById('resultsContent');
    
    let html = '';
    
    // Yearly breakdown if multiple years
    if (result.yearlyCalculations.length > 1) {
        html += '<h6 class="mb-3">التفصيل السنوي:</h6>';
        result.yearlyCalculations.forEach(calc => {
            html += `
                <div class="result-item">
                    <div class="result-label">سنة ${calc.year} (${calc.days} يوم)</div>
                    <div class="result-value">${formatCurrency(calc.allowance)}</div>
                    <div class="result-details">
                        الراتب: ${formatCurrency(calc.salary)}
                        ${calc.isCurrentSalaryBased ? ' (من المربوط الحالي)' : ''}
                    </div>
                </div>
            `;
        });
        
        html += '<hr class="my-3">';
    }
    
    // Total result
    html += `
        <div class="total-result">
            <div class="total-label">إجمالي البدل</div>
            <div class="total-value">${formatCurrency(result.totalAllowance)}</div>
            <div class="total-details">
                ${result.allowanceInfo.name} - ${result.totalDays} يوم
            </div>
        </div>
    `;
    
    resultsContent.innerHTML = html;
    resultsCard.style.display = 'block';
    
    // Scroll to results
    resultsCard.scrollIntoView({ behavior: 'smooth' });
}

// Display period details
function displayPeriodDetails(periodDetails) {
    const detailsCard = document.getElementById('periodDetails');
    const detailsContent = document.getElementById('periodDetailsContent');
    
    const html = `
        <div class="period-item">
            <span class="period-label">تاريخ البداية</span>
            <span class="period-value">${formatDateArabic(periodDetails.startDate)}</span>
        </div>
        <div class="period-item">
            <span class="period-label">تاريخ النهاية</span>
            <span class="period-value">${formatDateArabic(periodDetails.endDate)}</span>
        </div>
        <div class="period-item">
            <span class="period-label">إجمالي الأيام</span>
            <span class="period-value">${periodDetails.totalDays} يوم</span>
        </div>
        <div class="period-item">
            <span class="period-label">عدد السنوات</span>
            <span class="period-value">${periodDetails.years.length} سنة</span>
        </div>
        <div class="period-item">
            <span class="period-label">طريقة الحساب</span>
            <span class="period-value">30 يوم لكل شهر</span>
        </div>
    `;
    
    detailsContent.innerHTML = html;
    detailsCard.style.display = 'block';
}

// Utility functions
function formatCurrency(amount) {
    if (typeof amount !== 'number' || isNaN(amount)) {
        return '0.00 ر.س';
    }
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatDate(date) {
    if (!date || !(date instanceof Date)) {
        return '';
    }
    return date.toISOString().split('T')[0];
}

function formatDateArabic(date) {
    if (!date || !(date instanceof Date)) {
        return 'غير محدد';
    }
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(date);
}

// Make utility functions globally available
window.formatCurrency = formatCurrency;
window.formatDate = formatDate;
window.formatDateArabic = formatDateArabic;

// Print results
function printResults() {
    window.print();
}

// Export to PDF
function exportToPDF() {
    if (!validateFormForReport()) {
        return;
    }

    // Validate form function
    function validateFormForReport() {
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');
        const currentSalary = document.getElementById('currentSalary');
        
        if (!startDate || !endDate || !currentSalary) {
            alert('يرجى التأكد من تعبئة جميع الحقول');
            return false;
        }
        
        if (!startDate.value || !endDate.value || !currentSalary.value) {
            alert('يرجى تعبئة جميع الحقول المطلوبة');
            return false;
        }
    }
}
    detailsCard.style.display = 'block';

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatDate(date) {
    return date.toISOString().split('T')[0];
}

function formatDateArabic(date) {
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(date);
}

// Print results
function printResults() {
    window.print();
}

// Export to PDF
function exportToPDF() {
    if (typeof jsPDF === 'undefined') {
        alert('مكتبة PDF غير متوفرة');
        return;
    }
    
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();
    
    doc.setFont('helvetica');
    doc.setFontSize(16);
    
    doc.text('تقرير حساب بدل', 105, 20, { align: 'center' });
    
    doc.save('تقرير-بدل.pdf');
}

// Test function for date calculations (for debugging)
function testDateCalculations() {
    console.log('Testing 30-day month system:');
    
    // Test 1: Full year should be 360 days
    const jan1 = new Date(2024, 0, 1);
    const dec30 = new Date(2024, 11, 30);
    console.log(`Full year 2024: ${calculateDaysBetween(jan1, dec30)} days (should be 360)`);
    
    // Test 2: Full month should be 30 days
    const month1 = new Date(2024, 0, 1);
    const month30 = new Date(2024, 0, 30);
    console.log(`Full month January 2024: ${calculateDaysBetween(month1, month30)} days (should be 30)`);
    
    // Test 3: Multiple years
    const jan1_2023 = new Date(2023, 0, 1);
    const dec30_2024 = new Date(2024, 11, 30);
    console.log(`Multiple years (2023-2024): ${calculateDaysBetween(jan1_2023, dec30_2024)} days (should be 720)`);
}

// Select allowance and update form
function selectAllowance(allowanceId) {
    currentAllowance = allowances.find(a => a.id === allowanceId);
    
    if (!currentAllowance) {
        console.error('Allowance not found:', allowanceId);
        return;
    }
    
    console.log('Selected allowance:', currentAllowance);
    
    // Update UI
    updateAllowanceDisplay();
    
    // Check if we need yearly salary fields
    setTimeout(() => {
        checkAndShowYearlySalaryFields();
    }, 100);
    
    // Auto calculate if form is valid
    if (isFormValid()) {
        calculateAllowance();
    }
}

// Update allowance display
function updateAllowanceDisplay() {
    const allowanceSelect = document.getElementById('allowanceSelect');
    const selectedOption = allowanceSelect.querySelector(`option[value="${currentAllowance.id}"]`);
    const allowanceName = selectedOption.textContent;
    const allowanceDescription = selectedOption.getAttribute('data-description');
    const allowanceType = selectedOption.getAttribute('data-type');
    const allowanceValue = selectedOption.getAttribute('data-value');
    const allowanceIcon = selectedOption.getAttribute('data-icon');

    // Update allowance header
    const allowanceHeader = document.getElementById('allowanceHeader');
    allowanceHeader.innerHTML = `
        <div class="allowance-icon-large">
            <i class="${allowanceIcon}"></i>
        </div>
        <h2>${allowanceName}</h2>
        <p class="lead mb-3">${allowanceDescription}</p>
        <div class="row">
            <div class="col-md-6">
                <div class="text-center">
                    <small>نوع الحساب</small>
                    <div class="h5">${allowanceType === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت'}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="text-center">
                    <small>القيمة</small>
                    <div class="h5">${allowanceType === 'percentage' ? `${allowanceValue}%` : `${allowanceValue} ريال/يوم`}</div>
                </div>
            </div>
        </div>
    `;
}

// Setup event listeners
function setupEventListeners() {
    console.log('Setting up event listeners...');
    
    // Auto-calculate when inputs change
    const inputs = ['startDate', 'endDate', 'currentSalary', 'previousSalary'];
    inputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', handleInputChange);
            element.addEventListener('input', handleInputChange);
            console.log(`Added listeners to ${id}`);
        }
    });
    
    // Add button event listeners using IDs
    const calculateBtn = document.getElementById('calculateBtn');
    if (calculateBtn) {
        calculateBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Calculate button clicked');
            calculateAllowance();
        });
        console.log('Added calculate button listener');
    }
    
    const testBtn = document.getElementById('testBtn');
    if (testBtn) {
        testBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Test button clicked');
            testYearlyFields();
        });
        console.log('Added test button listener');
    }
    
    // Set default dates
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    
    if (startDateEl) startDateEl.value = formatDate(firstDayOfMonth);
    if (endDateEl) endDateEl.value = formatDate(today);
    
    console.log('Event listeners setup complete');
}

// Handle input changes
function handleInputChange() {
    // Check if we need to show yearly salary fields
    checkAndShowYearlySalaryFields();
    
    // Auto calculate with debounce
    clearTimeout(window.calculateTimeout);
    window.calculateTimeout = setTimeout(() => {
        if (isFormValid()) {
            calculateAllowance();
        }
    }, 500);
}

// Check if we need yearly salary fields
function checkAndShowYearlySalaryFields() {
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    
    if (!startDateEl || !endDateEl || !currentAllowance) return;
    
    const startDate = startDateEl.value;
    const endDate = endDateEl.value;
    
    if (!startDate || !endDate) return;
    
    const isCurrentSalaryBased = currentAllowance.description === 'من المربوط الحالي';
    const yearlySalariesContainer = document.getElementById('yearlySalariesContainer');
    const previousSalaryField = document.getElementById('previousSalaryField');
    
    console.log('Checking yearly fields:', {
        isCurrentSalaryBased,
        startDate,
        endDate,
        hasYearlyFields: !!yearlySalariesContainer
    });
    
    if (isCurrentSalaryBased) {
        const startYear = new Date(startDate).getFullYear();
        const endYear = new Date(endDate).getFullYear();
        const yearsDiff = endYear - startYear + 1;
        
        console.log('Years difference:', yearsDiff);
        
        if (yearsDiff > 1) {
            // Multiple years - show yearly salary fields
            const years = [];
            for (let year = startYear; year <= endYear; year++) {
                years.push(year);
            }
            
            generateYearlySalaryFields(years);
            if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'block';
            if (previousSalaryField) previousSalaryField.style.display = 'none';
        } else {
            // Single year - hide yearly fields
            if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'none';
            if (previousSalaryField) previousSalaryField.style.display = 'block';
        }
    } else {
        // Normal calculation - show previous salary field
        if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'none';
        if (previousSalaryField) previousSalaryField.style.display = 'block';
    }
}

// Generate yearly salary fields
function generateYearlySalaryFields(years) {
    const container = document.getElementById('yearlySalariesFields');
    if (!container) {
        console.error('yearlySalariesFields container not found');
        return;
    }
    
    console.log('Generating fields for years:', years);
    
    // Check if fields already exist for these years
    const existingFields = container.querySelectorAll('.yearly-salary-field');
    const existingYears = Array.from(existingFields).map(field => {
        const input = field.querySelector('input');
        return input ? parseInt(input.dataset.year) : null;
    }).filter(year => year !== null);
    
    // If the same years already exist, don't regenerate
    if (existingYears.length === years.length && 
        years.every(year => existingYears.includes(year))) {
        console.log('Fields already exist for these years, skipping generation');
        return;
    }
    
    // Store existing values before clearing
    const existingValues = {};
    existingFields.forEach(field => {
        const input = field.querySelector('input');
        if (input && input.value) {
            existingValues[input.dataset.year] = input.value;
        }
    });
    
    // Clear existing fields
    container.innerHTML = '';
    
    const currentYear = new Date().getFullYear();
    const currentSalaryEl = document.getElementById('currentSalary');
    const currentSalaryValue = currentSalaryEl ? currentSalaryEl.value : '';
    
    years.forEach((year, index) => {
        const fieldDiv = document.createElement('div');
        fieldDiv.className = 'mb-3 yearly-salary-field';
        
        let yearLabel = `راتب سنة ${year}`;
        let placeholder = `أدخل راتب سنة ${year}`;
        let defaultValue = '';
        
        // Restore existing value if available
        if (existingValues[year]) {
            defaultValue = existingValues[year];
        } else if (year === currentYear) {
            yearLabel += ' (السنة الحالية)';
            defaultValue = currentSalaryValue;
        } else if (year < currentYear) {
            yearLabel += ' (سنة سابقة)';
        } else {
            yearLabel += ' (سنة مستقبلية)';
        }
        
        fieldDiv.innerHTML = `
            <label for="salary_${year}" class="form-label">
                <i class="fas fa-calendar-year me-2"></i>
                ${yearLabel}
            </label>
            <input type="number" 
                   class="form-control yearly-salary-input" 
                   id="salary_${year}" 
                   data-year="${year}"
                   placeholder="${placeholder}"
                   min="0" 
                   step="0.01"
                   value="${defaultValue}">
        `;
        
        container.appendChild(fieldDiv);
        
        // Add event listener to the new field
        const input = fieldDiv.querySelector('input');
        if (input) {
            input.addEventListener('change', handleInputChange);
            input.addEventListener('input', handleInputChange);
        }
    });
    
    console.log(`Generated ${years.length} yearly salary fields`);
}

// Auto calculate with debounce
function autoCalculate() {
    clearTimeout(calculateTimeout);
    calculateTimeout = setTimeout(() => {
        if (isFormValid()) {
            calculateAllowance();
        }
    }, 500);
}

// Check if form is valid
function isFormValid() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const currentSalary = document.getElementById('currentSalary').value;
    
    console.log('Validating form:', { startDate, endDate, currentSalary });
    
    if (!startDate || !endDate || !currentSalary || parseFloat(currentSalary) <= 0) {
        console.log('Basic validation failed');
        return false;
    }
    
    // Check yearly salary fields if they are visible
    const isCurrentSalaryBased = currentAllowance && currentAllowance.description === 'من المربوط الحالي';
    const yearlySalariesContainer = document.getElementById('yearlySalariesContainer');
    
    if (isCurrentSalaryBased && yearlySalariesContainer && yearlySalariesContainer.style.display !== 'none') {
        console.log('Checking yearly salary fields...');
        const yearlyInputs = document.querySelectorAll('.yearly-salary-input');
        console.log('Found yearly inputs:', yearlyInputs.length);
        
        for (let input of yearlyInputs) {
            const value = parseFloat(input.value);
            console.log(`Checking input for year ${input.dataset.year}: ${value}`);
            if (!input.value || value <= 0) {
                console.log(`Validation failed for year ${input.dataset.year}`);
                return false;
            }
        }
    }
    
    console.log('Form validation passed');
    return true;
}

// Calculate allowance
function calculateAllowance() {
    if (!isFormValid()) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // Show calculating state
    document.body.classList.add('calculating');
    
    setTimeout(() => {
        try {
            const result = performCalculation();
            displayResults(result);
            displayPeriodDetails(result.periodDetails);
        } catch (error) {
            alert('حدث خطأ في الحساب: ' + error.message);
        } finally {
            document.body.classList.remove('calculating');
        }
    }, 500);
}

// Perform the actual calculation
function performCalculation() {
    const startDate = new Date(document.getElementById('startDate').value);
    const endDate = new Date(document.getElementById('endDate').value);
    const currentSalary = parseFloat(document.getElementById('currentSalary').value);
    const previousSalary = parseFloat(document.getElementById('previousSalary').value) || currentSalary;
    
    // Validate dates
    if (endDate <= startDate) {
        throw new Error('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
    }
    
    // Calculate period details
    const periodDetails = calculatePeriodDetails(startDate, endDate);
    
    // Check if this is "من المربوط الحالي" calculation
    const isCurrentSalaryBased = currentAllowance.description === 'من المربوط الحالي';
    
    // Get yearly salaries if needed
    const yearlySalaries = {};
    if (isCurrentSalaryBased && periodDetails.years.length > 1) {
        // Collect yearly salaries from dynamic fields
        let hasEmptyFields = false;
        
        periodDetails.years.forEach(yearData => {
            const salaryInput = document.getElementById(`salary_${yearData.year}`);
            if (salaryInput) {
                const salary = parseFloat(salaryInput.value);
                if (!salary || salary <= 0) {
                    hasEmptyFields = true;
                } else {
                    yearlySalaries[yearData.year] = salary;
                }
            }
        });
        
        if (hasEmptyFields) {
            throw new Error('يرجى إدخال راتب صحيح لجميع السنوات');
        }
    }
    
    // Calculate allowance for each year
    let totalAllowance = 0;
    const yearlyCalculations = [];
    
    periodDetails.years.forEach(yearData => {
        let salary;
        
        if (isCurrentSalaryBased && periodDetails.years.length > 1) {
            // Use yearly salaries from dynamic fields
            salary = yearlySalaries[yearData.year];
            if (!salary) {
                throw new Error(`لم يتم العثور على راتب سنة ${yearData.year}`);
            }
        } else if (isCurrentSalaryBased) {
            // Single year - use current salary
            salary = currentSalary;
        } else {
            // Normal calculation - use current salary for current year, previous for others
            const currentYear = new Date().getFullYear();
            salary = yearData.year === currentYear ? currentSalary : previousSalary;
        }
        
        let yearlyAllowance = 0;
        
        if (currentAllowance.type === 'percentage') {
            // Percentage calculation: salary × percentage ÷ 100 × days ÷ 365
            yearlyAllowance = (salary * currentAllowance.value / 100) * (yearData.days / 365);
        } else if (currentAllowance.type === 'fixed') {
            // Fixed amount calculation: fixed amount × days ÷ 365
            yearlyAllowance = currentAllowance.value * (yearData.days / 365);
        }
        
        totalAllowance += yearlyAllowance;
        
        yearlyCalculations.push({
            year: yearData.year,
            salary: salary,
            days: yearData.days,
            allowance: yearlyAllowance,
            percentage: currentAllowance.type === 'percentage' ? currentAllowance.value : null
        });
    });
    
    return {
        totalAllowance: totalAllowance,
        yearlyCalculations: yearlyCalculations,
        periodDetails: periodDetails,
        allowanceInfo: currentAllowance
    };
}

// Calculate period details (30 days per month, 360 days per year)
function calculatePeriodDetails(startDate, endDate) {
    const startYear = startDate.getFullYear();
    const startMonth = startDate.getMonth() + 1;
    const startDay = startDate.getDate();
    
    const endYear = endDate.getFullYear();
    const endMonth = endDate.getMonth() + 1;
    const endDay = endDate.getDate();
    
    // Calculate total days using 30-day month system
    const totalDays = calculateDaysBetween(startDate, endDate);
    
    // Get years in the period
    const years = [];
    for (let year = startYear; year <= endYear; year++) {
        const yearStartDate = year === startYear ? startDate : new Date(year, 0, 1);
        const yearEndDate = year === endYear ? endDate : new Date(year, 11, 30);
        
        const yearDays = calculateDaysBetween(yearStartDate, yearEndDate);
        
        years.push({
            year: year,
            days: yearDays,
            startDate: yearStartDate,
            endDate: yearEndDate
        });
    }
    
    return {
        totalDays: totalDays,
        years: years,
        startDate: startDate,
        endDate: endDate
    };
}

// Calculate days between two dates (30 days per month system)
function calculateDaysBetween(start, end) {
    const startYear = start.getFullYear();
    const startMonth = start.getMonth() + 1;
    const startDay = start.getDate();
    
    const endYear = end.getFullYear();
    const endMonth = end.getMonth() + 1;
    const endDay = end.getDate();
    
    const adjustedStartDay = Math.min(startDay, 30);
    const adjustedEndDay = Math.min(endDay, 30);
    
    const totalStartMonths = (startYear * 12) + startMonth;
    const totalEndMonths = (endYear * 12) + endMonth;
    
    let totalDays = 0;
    
    if (totalStartMonths === totalEndMonths) {
        totalDays = adjustedEndDay - adjustedStartDay + 1;
    } else {
        totalDays += (30 - adjustedStartDay + 1);
        const monthsBetween = totalEndMonths - totalStartMonths - 1;
        totalDays += monthsBetween * 30;
        totalDays += adjustedEndDay;
    }
    
    return totalDays;
}
function performCalculation() {
    const currentSalary = parseFloat(document.getElementById('currentSalary').value) || 0;
    const newSalary = parseFloat(document.getElementById('newSalary').value) || 0;

    const previousSalaryEl = document.getElementById('previousSalary');
    const previousSalary = previousSalaryEl ? parseFloat(previousSalaryEl.value) : currentSalary;

    const numYears = parseInt(document.getElementById('numYears').value) || 1;

    let salaryDifferences = [];
    let totalDifference = 0;

    for (let i = 0; i < numYears; i++) {
        const currentYearSalary = parseFloat(document.getElementById(`currentSalary${i}`).value) || 0;
        const newYearSalary = parseFloat(document.getElementById(`newSalary${i}`).value) || 0;

        const difference = newYearSalary - currentYearSalary;
        salaryDifferences.push(difference);
        totalDifference += difference;
    }

    document.getElementById('result').innerHTML = `
        <div class="alert alert-success">
            الفرق الكلي في الراتب: ${totalDifference.toFixed(2)} ريال
        </div>
    `;

    const breakdown = salaryDifferences.map((diff, index) => {
        return `<li>السنة ${index + 1}: ${diff.toFixed(2)} ريال</li>`;
    }).join('');

    document.getElementById('details').innerHTML = `
        <ul>${breakdown}</ul>
    `;
}
