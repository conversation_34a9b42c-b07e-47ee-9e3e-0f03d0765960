/* Salary Difference Page Military Theme */

/* CSS Variables */
:root {
    --military-dark: #2d3436;
    --military-olive: #6c5ce7;
    --military-green: #00b894;
    --military-light-green: #55efc4;
    --military-white: #ffffff;
    --military-gray: #636e72;
    --military-gold: #fdcb6e;
    --shadow-color: rgba(45, 52, 54, 0.3);
}

.salary-header-card {
    background: linear-gradient(135deg, var(--military-dark), var(--military-olive), var(--military-green));
    color: var(--military-white);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 
        0 20px 40px var(--shadow-color),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.salary-header-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(253, 203, 110, 0.1) 0%, transparent 50%);
    animation: rotate 25s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.salary-icon-large {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.9;
    animation: salaryPulse 2.5s ease-in-out infinite;
    color: var(--military-gold);
    filter: drop-shadow(0 6px 12px rgba(253, 203, 110, 0.4));
    position: relative;
    z-index: 1;
}

@keyframes salaryPulse {
    0%, 100% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.05) rotate(-2deg); }
    50% { transform: scale(1.1) rotate(1deg); }
    75% { transform: scale(1.05) rotate(-1deg); }
}

.stat-item {
    padding: 0.5rem;
    font-size: 0.9rem;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.stat-item i {
    display: block;
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--military-light-green);
    animation: iconFloat 3s ease-in-out infinite;
}

.stat-item:nth-child(2) i { animation-delay: 0.5s; }
.stat-item:nth-child(3) i { animation-delay: 1s; }

@keyframes iconFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

/* Salary Fields Container */
.salary-fields-container {
    max-height: 500px;
    overflow-y: auto;
    padding: 1rem;
    background: linear-gradient(145deg, #f8f9fa, #ffffff);
    border-radius: 15px;
    border: 2px solid rgba(0, 184, 148, 0.1);
    margin-bottom: 1rem;
}

.salary-year-field {
    background: linear-gradient(145deg, var(--military-white), #f1f2f6);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--military-green);
    box-shadow: 0 5px 15px rgba(0, 184, 148, 0.1);
    transition: all 0.3s ease;
    animation: slideInRight 0.5s ease-out;
}

.salary-year-field:hover {
    transform: translateX(-5px);
    box-shadow: 0 8px 25px rgba(0, 184, 148, 0.2);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.year-label {
    font-weight: 600;
    color: var(--military-dark);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
}

.year-label i {
    color: var(--military-green);
    margin-left: 0.5rem;
    animation: yearIconSpin 4s linear infinite;
}

@keyframes yearIconSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.salary-input {
    border: 2px solid rgba(108, 92, 231, 0.2);
    border-radius: 10px;
    padding: 0.75rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.salary-input:focus {
    border-color: var(--military-green);
    box-shadow: 0 0 0 0.2rem rgba(0, 184, 148, 0.25);
    background: var(--military-white);
    transform: scale(1.02);
}

/* Results Styling */
.difference-item {
    animation: slideInUp 0.6s ease-out;
    padding: 1rem;
    margin: 0.5rem 0;
    border-radius: 12px;
    background: linear-gradient(145deg, #ffffff, #f1f2f6);
    border-left: 4px solid var(--military-olive);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.difference-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(108, 92, 231, 0.05), transparent);
    transition: left 0.5s;
}

.difference-item:hover::before {
    left: 100%;
}

.difference-item:hover {
    transform: translateX(10px);
    box-shadow: 0 8px 20px rgba(108, 92, 231, 0.2);
}

.difference-positive {
    border-left-color: var(--military-green);
    background: linear-gradient(145deg, #ffffff, rgba(0, 184, 148, 0.05));
}

.difference-negative {
    border-left-color: #e74c3c;
    background: linear-gradient(145deg, #ffffff, rgba(231, 76, 60, 0.05));
}

.difference-zero {
    border-left-color: var(--military-gray);
    background: linear-gradient(145deg, #ffffff, rgba(99, 110, 114, 0.05));
}

.total-difference {
    background: linear-gradient(135deg, var(--military-olive), var(--military-green)) !important;
    color: var(--military-white) !important;
    border: none !important;
    animation: totalGlow 2s ease-in-out infinite;
    position: relative;
    overflow: hidden;
    font-size: 1.1rem;
    padding: 1.5rem !important;
}

.total-difference::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shine 3s ease-in-out infinite;
}

.total-difference .small {
    color: rgba(255, 255, 255, 0.8) !important;
    font-style: italic;
}

/* Calculation note styling */
.calculation-note {
    background: linear-gradient(145deg, #fff3cd, #ffeaa7);
    border-left: 4px solid var(--military-gold) !important;
    font-style: italic;
}

/* Enhanced difference items */
.difference-item .small {
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.difference-item .text-primary {
    color: var(--military-olive) !important;
    font-weight: 600;
}

/* Period difference highlight */
.period-difference-highlight {
    background: linear-gradient(145deg, #e8f5e8, #d4edda);
    border-left: 4px solid var(--military-green);
    animation: highlightPulse 2s ease-in-out infinite;
}

@keyframes highlightPulse {
    0%, 100% { box-shadow: 0 4px 12px rgba(0, 184, 148, 0.2); }
    50% { box-shadow: 0 8px 20px rgba(0, 184, 148, 0.4); }
}

/* Success bounce animation */
.success-bounce {
    animation: successBounce 0.6s ease-out;
}

@keyframes successBounce {
    0% { transform: scale(0.95); opacity: 0.8; }
    50% { transform: scale(1.02); opacity: 1; }
    100% { transform: scale(1); opacity: 1; }
}

/* Enhanced responsive design for calculations */
@media (max-width: 768px) {
    .total-difference {
        font-size: 1rem;
        padding: 1rem !important;
    }
    
    .difference-item .small {
        font-size: 0.75rem;
    }
    
    .calculation-note {
        font-size: 0.85rem;
    }
}

@keyframes totalGlow {
    0%, 100% { box-shadow: 0 8px 20px rgba(108, 92, 231, 0.3); }
    50% { box-shadow: 0 12px 30px rgba(108, 92, 231, 0.5); }
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Ministry Logo */
.ministry-logo-white {
    max-width: 100px;
    height: auto;
    filter: brightness(0) invert(1) drop-shadow(0 4px 8px rgba(255, 255, 255, 0.3));
    object-fit: contain;
    animation: logoFloat 3s ease-in-out infinite;
    position: relative;
    z-index: 1;
}

@keyframes logoFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .salary-header-card {
        padding: 1.5rem;
    }
    
    .salary-icon-large {
        font-size: 3.5rem;
    }
}

@media (max-width: 768px) {
    .salary-header-card {
        padding: 1.5rem;
    }
    
    .salary-icon-large {
        font-size: 3rem;
    }
    
    .salary-fields-container {
        max-height: 400px;
        padding: 0.5rem;
    }
    
    .salary-year-field {
        padding: 0.75rem;
    }
    
    .ministry-logo-white {
        max-width: 70px;
    }
    
    .stat-item {
        font-size: 0.8rem;
    }
    
    .stat-item i {
        font-size: 1.2rem;
    }
}

/* Print Styles */
@media print {
    .salary-header-card {
        background: linear-gradient(135deg, var(--military-dark), var(--military-olive)) !important;
        color: white !important;
        padding: 15px !important;
        margin-bottom: 15px !important;
    }
    
    .salary-icon-large {
        font-size: 24px !important;
    }
    
    .ministry-logo-white {
        max-width: 60px !important;
    }
    
    .difference-item {
        font-size: 11px !important;
        padding: 8px !important;
        margin: 5px 0 !important;
    }
    
    .total-difference {
        background: linear-gradient(135deg, var(--military-green), var(--military-light-green)) !important;
        color: white !important;
        font-size: 14px !important;
        font-weight: 700 !important;
    }
}

/* Toast Notifications */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, var(--military-green), var(--military-light-green));
    color: var(--military-white);
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: 0 8px 20px rgba(243, 244, 244, 0.3);
    transform: translateX(400px);
    transition: all 0.3s ease;
    z-index: 9999;
    font-weight: 500;
}

.toast-notification.show {
    transform: translateX(0);
}

.toast-notification.toast-success {
    background: linear-gradient(135deg, var(--military-green), var(--military-light-green));
}

.toast-notification.toast-error {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.toast-content {
    display: flex;
    align-items: center;
}

/* Period Item Styling */
.period-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    margin: 0.5rem 0;
    background: linear-gradient(145deg, #ffffff, #f1f2f6);
    border-radius: 8px;
    border-left: 3px solid var(--military-green);
    transition: all 0.3s ease;
}

.period-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 12px rgba(0, 184, 148, 0.15);
}

.period-label {
    font-weight: 500;
    color: var(--military-dark);
}

.period-value {
    font-weight: 600;
    color: var(--military-green);
}

/* Enhanced Button Styling */
.btn-primary {
    background: linear-gradient(135deg, var(--military-olive), var(--military-green));
    border: none;
    border-radius: 12px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(108, 92, 231, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(108, 92, 231, 0.4);
    background: linear-gradient(135deg, var(--military-green), var(--military-light-green));
}

.btn-success {
    background: linear-gradient(135deg, var(--military-green), var(--military-light-green));
    border: none;
    border-radius: 12px;
    padding: 15px 40px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 8px 20px rgba(0, 184, 148, 0.3);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(0, 184, 148, 0.4);
    background: linear-gradient(135deg, var(--military-light-green), var(--military-gold));
}

/* Form Validation */
.is-invalid {
    border-color: #e74c3c !important;
    box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25) !important;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Loading State */
.calculating {
    pointer-events: none;
    opacity: 0.7;
}

.calculating .btn {
    position: relative;
}

.calculating .btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced Responsive Design */
@media (max-width: 576px) {
    .toast-notification {
        right: 10px;
        left: 10px;
        transform: translateY(-100px);
    }
    
    .toast-notification.show {
        transform: translateY(0);
    }
    
    .btn-primary, .btn-success {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    
    .salary-year-field {
        padding: 0.75rem;
        margin-bottom: 0.75rem;
    }
    
    .year-label {
        font-size: 0.9rem;
    }
    
    .salary-input {
        padding: 0.5rem;
        font-size: 0.9rem;
    }
}

/* Calculation formula styling */
.calculation-formula {
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 0.75rem;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    color: var(--military-olive);
    font-weight: 600;
    direction: ltr;
    text-align: center;
}

/* Neutral difference styling */
.difference-neutral {
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border-left: 4px solid #6c757d;
}

/* Enhanced difference items */
.difference-item .small {
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.difference-item .text-primary {
    color: var(--military-olive) !important;
    font-weight: 600;
}

/* Year comparison styling */
.year-comparison {
    background: linear-gradient(145deg, #fff, #f8f9fa);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    position: relative;
}

.year-comparison::before {
    content: '→';
    position: absolute;
    right: 50%;
    top: 50%;
    transform: translate(50%, -50%);
    font-size: 1.5rem;
    color: var(--military-olive);
    background: white;
    padding: 0.25rem 0.5rem;
    border-radius: 50%;
    border: 2px solid var(--military-olive);
}

/* Enhanced total difference styling */
.total-difference {
    background: linear-gradient(135deg, var(--military-olive), var(--military-green)) !important;
    color: var(--military-white) !important;
    border: none !important;
    animation: totalGlow 2s ease-in-out infinite;
    position: relative;
    overflow: hidden;
    font-size: 1.1rem;
    padding: 1.5rem !important;
}

.total-difference::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shine 3s ease-in-out infinite;
}

.total-difference .small {
    color: rgba(255, 255, 255, 0.8) !important;
    font-style: italic;
}

/* Responsive design for formulas */
@media (max-width: 768px) {
    .calculation-formula {
        font-size: 0.75rem;
        padding: 0.5rem;
        direction: rtl;
        text-align: right;
    }
    
    .year-comparison::before {
        content: '↓';
        right: 50%;
        top: 30%;
    }
    
    .difference-item .small {
        font-size: 0.75rem;
    }
}

/* Print styles */
@media print {
    .calculation-formula {
        background: #f8f9fa !important;
        border: 1px solid #000 !important;
    }
    
    .total-difference::before {
        display: none;
    }
}


