<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حساب البدل - نظام حساب البدلات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/allowance.css">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-calculator me-2"></i>
                نظام حساب البدلات
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.html">
                    <i class="fas fa-arrow-right me-1"></i>
                    العودة للرئيسية
                </a>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container mt-4">
        <!-- رأس البدل -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="allowance-header-card" id="allowanceHeader">
                    <!-- سيتم تعبئته بواسطة جافاسكريبت -->
                </div>
            </div>
        </div>

        <div class="row">
            <!-- نموذج الإدخال -->
            <div class="col-lg-6">
                <div class="calculation-card">
                    <div class="card-header">
                        <h5><i class="fas fa-edit me-2"></i>بيانات الحساب</h5>
                    </div>
                    <div class="card-body">
                        <form id="calculationForm">
                            <!-- نطاق التاريخ -->
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="startDate" class="form-label">تاريخ البداية</label>
                                    <input type="date" class="form-control" id="startDate" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="endDate" class="form-label">تاريخ النهاية</label>
                                    <input type="date" class="form-control" id="endDate" required>
                                </div>
                            </div>
                            
                            <!-- معلومات طريقة الحساب -->
                            <div class="alert alert-info mb-3">
                                <i class="fas fa-info-circle me-2"></i>
                                <strong>نظام الحساب:</strong> كل شهر = 30 يوم، السنة = 360 يوم
                            </div>

                            <!-- Current Salary -->
                            <div class="mb-3">
                                <label for="currentSalary" class="form-label">الراتب الحالي (ريال)</label>
                                <input type="number" class="form-control" id="currentSalary" required min="0" step="0.01">
                            </div>

                            <!-- Dynamic Yearly Salaries Container -->
                            <div id="yearlySalariesContainer" style="display: none;">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>حساب من المربوط الحالي:</strong> يرجى إدخال راتب كل سنة في الفترة المحددة
                                </div>
                                <div id="yearlySalariesFields">
                                    <!-- Dynamic salary fields will be generated here -->
                                </div>
                            </div>

                            <!-- زر الحساب -->
                            <button type="button" class="btn btn-primary btn-lg w-100" id="calculateBtn">
                                <i class="fas fa-calculator me-2"></i>
                                احسب الاستحقاق
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- النتائج -->
            <div class="col-lg-6">
                <div class="results-card" id="resultsCard" style="display: none;">
                    <div class="card-header">
                        <h5><i class="fas fa-chart-line me-2"></i>نتائج الحساب</h5>
                    </div>
                    <div class="card-body" id="resultsContent">
                        <!-- سيتم تعبئة النتائج هنا -->
                    </div>
                    <div class="card-footer">
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button class="btn btn-outline-primary" onclick="window.printResults()">
                                <i class="fas fa-print me-1"></i>
                                طباعة
                            </button>
                            <button class="btn btn-outline-success" onclick="window.exportToPDF()">
                                <i class="fas fa-file-pdf me-1"></i>
                                تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- تفاصيل الفترة -->
                <div class="period-details-card" id="periodDetails" style="display: none;">
                    <div class="card-header">
                        <h6><i class="fas fa-calendar-alt me-2"></i>تفاصيل المدة</h6>
                    </div>
                    <div class="card-body" id="periodDetailsContent">
                        <!-- سيتم تعبئة تفاصيل الفترة هنا -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تذييل الطباعة (مخفي على الشاشة، مرئي عند الطباعة) -->
    <div class="print-footer" style="display: none;">
        <div>نظام حساب البدلات - فرع الشؤون الإدارية والمالية للقوات البرية بالجنوبية - معهد سلاح المشاة</div>
        <div>تاريخ الطباعة: <span id="printDate"></span></div>
    </div>

    <!-- قالب الطباعة الاحترافي (مخفي على الشاشة) -->
    <div id="printTemplate" style="display: none;">
        <div class="professional-report">
            <div class="report-header">
                <div class="ministry-logo-text">وزارة الدفاع - القوات البرية الملكية السعودية</div>
                <div class="department-title">فرع الشؤون الإدارية والمالية للقوات البرية بالجنوبية - معهد سلاح المشاة</div>
                <div class="report-title">تقرير حساب البدل</div>
                <div class="report-date">التاريخ: <span id="reportDate"></span></div>
            </div>
            
            <div class="report-content">
                <div class="employee-info" id="printEmployeeInfo">
                    <!-- سيتم تعبئة معلومات الموظف -->
                </div>
                
                <div class="calculation-details" id="printCalculationDetails">
                    <!-- سيتم تعبئة تفاصيل الحساب -->
                </div>
                
                <div class="results-summary" id="printResultsSummary">
                    <!-- سيتم تعبئة النتائج -->
                </div>
            </div>
            
            <div class="report-footer">
                <div class="signature-section">
                    <div class="signature-box">
                        <div>المحاسب</div>
                        <div class="signature-line"></div>
                        <div>التوقيع والتاريخ</div>
                    </div>
                    <div class="signature-box">
                        <div>رئيس القسم</div>
                        <div class="signature-line"></div>
                        <div>التوقيع والتاريخ</div>
                    </div>
                </div>
                <div class="system-info">
                    نظام حساب البدلات الإلكتروني - معهد سلاح المشاة
                </div>
            </div>
        </div>
    </div>

    <!-- مكتبة بوتستراب جافاسكريبت -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- مكتبة jsPDF لتصدير PDF -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    
    <!-- ملف الجافاسكريبت المخصص -->
    <script src="assets/js/allowance_fixed.js"></script>
    <script src="assets/js/allowance-print-report.js"></script>
    <script>
        // تعيين تاريخ الطباعة
        document.getElementById('printDate').textContent = new Date().toLocaleDateString('ar-SA');

        // Show/hide current salary field based on allowance type
        function toggleSalaryField() {
            const currentSalaryField = document.querySelector('.mb-3:has(#currentSalary)');
            if (currentAllowance && currentAllowance.type === 'fixed') {
                currentSalaryField.style.display = 'none';
                document.getElementById('currentSalary').removeAttribute('required');
            } else {
                currentSalaryField.style.display = 'block';
                document.getElementById('currentSalary').setAttribute('required', 'required');
            }
        }

        // Call this function when allowance is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // ... existing code ...
            toggleSalaryField();
        });
    </script>
</body>
</html>
