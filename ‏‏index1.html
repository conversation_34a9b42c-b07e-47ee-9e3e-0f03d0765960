<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>نظام حساب البدلات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    <style>
        /* Professional Modern Design */
        * {
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            margin: 0;
            padding: 0;
        }
        
        :root {
            --primary: #4361ee;
            --secondary: #3f37c9;
            --accent: #4cc9f0;
            --light: #f8f9fa;
            --dark: #212529;
            --success: #06ffa5;
            --warning: #ffbe0b;
            --danger: #fb5607;
            --info: #3a86ff;
            --purple: #7209b7;
            --pink: #f72585;
            --gradient-1: linear-gradient(135deg, var(--primary), var(--secondary));
            --gradient-2: linear-gradient(135deg, var(--accent), var(--info));
            --gradient-3: linear-gradient(135deg, var(--pink), var(--purple));
            --shadow-1: 0 4px 20px rgba(0, 0, 0, 0.08);
            --shadow-2: 0 8px 30px rgba(0, 0, 0, 0.12);
            --shadow-3: 0 15px 35px rgba(0, 0, 0, 0.15);
            --radius: 16px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            font-size: 16px;
            line-height: 1.6;
            color: var(--dark);
            background: #f0f2f5;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
            padding-bottom: 80px;
        }
        
        .container {
            padding: 0 15px;
            max-width: 100%;
            margin: 0 auto;
        }
        
        /* Navigation adjustments */
        .navbar {
            padding: 15px 0;
            background: var(--gradient-1) !important;
            box-shadow: var(--shadow-2);
            position: sticky;
            top: 0;
            z-index: 1000;
            border: none;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }
        
        .navbar-brand {
            font-size: 1.2rem;
            font-weight: 700;
            color: white !important;
            display: flex;
            align-items: center;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .navbar-brand i {
            background: rgba(255, 255, 255, 0.2);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
        }
        
        .navbar-toggler {
            border: none;
            color: white;
        }
        
        .navbar-toggler:focus {
            box-shadow: none;
        }
        
        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }
        
        /* Welcome card adjustments */
        .welcome-card {
            padding: 30px 25px;
            border-radius: var(--radius);
            margin: 20px 0 30px;
            box-shadow: var(--shadow-3);
            background: var(--gradient-1);
            color: white;
            position: relative;
            overflow: hidden;
            transform: translateY(0);
            transition: var(--transition);
        }
        
        .welcome-card::before {
            content: "";
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%);
            transform: rotate(45deg);
            z-index: 1;
        }
        
        .welcome-card > * {
            position: relative;
            z-index: 2;
        }
        
        .display-9 {
            font-size: 2rem;
            font-weight: 800;
            margin-bottom: 15px;
            line-height: 1.2;
            text-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
        
        .financial-icon {
            color: var(--accent);
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }
        
        .ministry-logo {
            max-width: 120px;
            height: auto;
            margin-bottom: 20px;
            filter: drop-shadow(0 4px 6px rgba(0,0,0,0.2));
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.2);
            padding: 8px;
        }
        
        /* Stat cards adjustments */
        .stat-card {
            margin-bottom: 20px;
            border-radius: var(--radius);
            box-shadow: var(--shadow-2);
            transition: var(--transition);
            background-color: white;
            padding: 20px;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .stat-card::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 6px;
            height: 100%;
            background: var(--gradient-1);
            border-radius: 3px 0 0 3px;
        }
        
        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: var(--shadow-3);
        }
        
        .stat-icon {
            width: 70px;
            height: 70px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            margin-left: 20px;
            color: white;
            box-shadow: var(--shadow-1);
            background: var(--gradient-1);
        }
        
        .stat-card:nth-child(2) .stat-icon {
            background: var(--gradient-2);
        }
        
        .stat-card:nth-child(3) .stat-icon {
            background: var(--gradient-3);
        }
        
        .stat-content {
            flex: 1;
        }
        
        .stat-content h3 {
            font-size: 2rem;
            font-weight: 800;
            margin: 0;
            background: var(--gradient-1);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-card:nth-child(2) .stat-content h3 {
            background: var(--gradient-2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-card:nth-child(3) .stat-content h3 {
            background: var(--gradient-3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stat-content p {
            margin: 5px 0 0;
            color: #6c757d;
            font-weight: 500;
            font-size: 1rem;
        }
        
        /* Button adjustments */
        .btn-lg {
            padding: 18px 24px;
            font-size: 1.1rem;
            margin-bottom: 16px;
            width: 100%;
            border-radius: var(--radius);
            font-weight: 700;
            box-shadow: var(--shadow-2);
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
            position: relative;
            overflow: hidden;
            z-index: 1;
        }
        
        .btn-lg::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(-100%);
            transition: var(--transition);
            z-index: -1;
        }
        
        .btn-lg:hover::before {
            transform: translateX(0);
        }
        
        .btn-lg:active {
            transform: scale(0.96);
        }
        
        .btn-warning {
            background: var(--gradient-2) !important;
            border: none;
            color: white !important;
        }
        
        .btn-info {
            background: var(--gradient-1) !important;
            border: none;
            color: white !important;
        }
        
        .btn-success {
            background: var(--gradient-3) !important;
            border: none;
            color: white !important;
        }
        
        /* Modal adjustments */
        .modal-dialog {
            margin: 10px;
            width: auto;
        }
        
        .modal-content {
            border-radius: 15px;
            border: none;
            box-shadow: 0 5px 20px rgba(0,0,0,0.2);
        }
        
        .modal-header {
            border-bottom: 1px solid #eee;
            padding: 15px 20px;
        }
        
        .modal-footer {
            border-top: 1px solid #eee;
            padding: 15px 20px;
        }
        
        /* Allowances grid adjustments */
        .allowance-card {
            margin-bottom: 15px;
            border-radius: 12px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.08);
            background-color: white;
            padding: 15px;
            transition: transform 0.3s ease;
        }
        
        .allowance-card:hover {
            transform: translateY(-5px);
        }
        
        /* Form adjustments */
        .form-control, .form-select {
            font-size: 16px; /* Prevents zoom on iOS */
            padding: 12px 15px;
            border-radius: 8px;
            border: 1px solid #ced4da;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #0065ff;
            box-shadow: 0 0 0 0.25rem rgba(0, 101, 255, 0.25);
        }
        
        /* Touch-friendly targets */
        .btn, .nav-link {
            min-height: 44px; /* Recommended minimum touch target size */
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
        }
        
        /* Money rain animation adjustments */
        .money-rain {
            position: absolute;
            font-size: 2rem;
            opacity: 0.15;
            animation: moneyFall 12s linear infinite;
            z-index: 0;
            color: white;
            filter: blur(1px);
        }
        
        @keyframes moneyFall {
            0% {
                transform: translateY(-100px) rotate(0deg) scale(0.8);
                opacity: 0;
            }
            10% {
                opacity: 0.15;
            }
            90% {
                opacity: 0.15;
            }
            100% {
                transform: translateY(calc(100vh + 100px)) rotate(360deg) scale(1.2);
                opacity: 0;
            }
        }
        
        /* Floating animation */
        @keyframes float {
            0% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-10px);
            }
            100% {
                transform: translateY(0px);
            }
        }
        
        .floating {
            animation: float 4s ease-in-out infinite;
        }
        
        /* Bottom navigation for mobile */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-around;
            padding: 12px 0 20px;
            z-index: 1000;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .bottom-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            text-decoration: none;
            font-size: 0.85rem;
            transition: var(--transition);
            position: relative;
            padding: 0 10px;
        }
        
        .bottom-nav-item.active {
            color: var(--primary);
        }
        
        .bottom-nav-item.active::before {
            content: "";
            position: absolute;
            top: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 4px;
            background: var(--gradient-1);
            border-radius: 2px;
        }
        
        .bottom-nav-item i {
            font-size: 1.8rem;
            margin-bottom: 6px;
            transition: var(--transition);
        }
        
        .bottom-nav-item.active i {
            transform: translateY(-3px);
        }
        
        /* Hide desktop navigation on mobile */
        .navbar-nav {
            display: none;
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            /* General adjustments */
            body {
                font-size: 16px;
                padding: 0;
                margin: 0;
            }
            
            .container {
                padding: 0 10px;
            }
            
            /* Navigation adjustments */
            .navbar-brand {
                font-size: 1.1rem;
            }
            
            /* Welcome card adjustments */
            .welcome-card {
                padding: 20px;
                border-radius: 15px;
                margin-bottom: 20px;
            }
            
            .display-9 {
                font-size: 1.8rem;
            }
            
            .ministry-logo {
                max-width: 100px;
                height: auto;
            }
            
            /* Stat cards adjustments */
            .stat-card {
                margin-bottom: 15px;
                border-radius: 12px;
            }
            
            .stat-icon {
                width: 60px;
                height: 60px;
                font-size: 24px;
            }
            
            /* Button adjustments */
            .btn-lg {
                padding: 12px 20px;
                font-size: 1rem;
                margin-bottom: 10px;
                width: 100%;
            }
            
            /* Modal adjustments */
            .modal-dialog {
                margin: 10px;
                width: auto;
            }
            
            .modal-content {
                border-radius: 15px;
            }
            
            /* Allowances grid adjustments */
            .allowance-card {
                margin-bottom: 15px;
                border-radius: 12px;
            }
            
            /* Form adjustments */
            .form-control, .form-select {
                font-size: 16px; /* Prevents zoom on iOS */
            }
            
            /* Touch-friendly targets */
            .btn, .nav-link {
                min-height: 44px; /* Recommended minimum touch target size */
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            /* Money rain animation adjustments */
            .money-rain {
                position: absolute;
                font-size: 1.5rem;
                opacity: 0.7;
                animation: moneyFall 8s linear infinite;
                z-index: -1;
            }
            
            @keyframes moneyFall {
                0% {
                    transform: translateY(-100px) rotate(0deg);
                }
                100% {
                    transform: translateY(calc(100vh + 100px)) rotate(360deg);
                }
            }
        }
        
        /* Extra small devices */
        @media (max-width: 576px) {
            .display-9 {
                font-size: 1.5rem;
            }
            
            .h4 {
                font-size: 1.1rem;
            }
            
            .stat-card {
                padding: 15px;
            }
            
            .stat-icon {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
            
            .btn-lg {
                padding: 10px 15px;
                font-size: 0.9rem;
            }
        }
        
        /* Landscape mode adjustments */
        @media (max-width: 768px) and (orientation: landscape) {
            .welcome-card {
                padding: 15px;
            }
            
            .display-9 {
                font-size: 1.4rem;
            }
            
            .money-rain {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-calculator me-2"></i>
                نظام حساب البدلات
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="scholarship.html">بدل الابتعاث</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showAddAllowanceModal()">إضافة بدل جديد</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container mt-4">
        <!-- قسم الترحيب -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="welcome-card">
                    <div class="text-center mb-4">
                        <img src="assets/images/ministry-logo2.png" alt="شعار وزارة الدفاع" class="ministry-logo">
                    </div>
                    <h1 class="display-9 text-center mb-3 responsive-title floating">
                        <i class="fas fa-coins financial-icon me-3"></i>
                        نظام حساب البدلات والعلاوات
                        <i class="fas fa-chart-line financial-icon ms-3"></i>
                    </h1>
                    <p class="lead text-center mb-4" style="font-size: 1.2rem; font-weight: 500; opacity: 0.9;">احسب استحقاقاتك المالية بسهولة ودقة</p>
                    <h2 class="h4 text-center mb-3" style="color: rgba(255,255,255,0.9); font-weight: 600;">
                        <i class="fas fa-shield-alt me-2"></i>
                        فرع الشؤون الإدارية والمالية للقوات البرية بالجنوبية
                    </h2>
                    <!-- أيقونات النقود المتحركة -->
                    <div class="money-rain" style="left: 10%; animation-delay: 0s;">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="money-rain" style="left: 20%; animation-delay: 1s;">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="money-rain" style="left: 80%; animation-delay: 2s;">
                        <i class="fas fa-money-bill"></i>
                    </div>
                    <div class="money-rain" style="left: 90%; animation-delay: 3s;">
                        <i class="fas fa-coins"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalAllowances">0</h3>
                        <p>إجمالي البدلات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="percentageAllowances">0</h3>
                        <p>بدلات نسبية</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="fixedAllowances">0</h3>
                        <p>بدلات ثابتة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- زر إضافة بدل جديد -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <a href="scholarship.html" class="btn btn-warning btn-lg mb-2 mobile-btn">
                    <i class="fas fa-graduation-cap me-2"></i>
                    حساب بدل الابتعاث
                </a>
                <a href="salary-difference.html" class="btn btn-info btn-lg mb-2 mobile-btn">
                    <i class="fas fa-chart-line me-2"></i>
                    حساب فرق الراتب
                </a>
                <button class="btn btn-success btn-lg mb-2 mobile-btn" onclick="showAddAllowanceModal()">
                    <i class="fas fa-plus me-2"></i>
                    إضافة بدل جديد
                </button>
            </div>
        </div>

        <!-- شبكة البدلات -->
        <div class="row" id="allowancesGrid">
            <!-- سيتم تحميل البدلات هنا -->
        </div>
    </div>

    <!-- نافذة إضافة البدل -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <div class="modal fade" id="addAllowanceModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة بدل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addAllowanceForm">
                        <div class="mb-3">
                            <label for="allowanceName" class="form-label">اسم البدل</label>
                            <input type="text" class="form-control" id="allowanceName" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="allowanceDescription" class="form-label">وصف البدل</label>
                            <textarea class="form-control" id="allowanceDescription" rows="2"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="calculationType" class="form-label">طريقة الحساب</label>
                            <select class="form-select" id="calculationType" onchange="toggleCalculationFields()">
                                <option value="percentage">نسبة مئوية من الراتب</option>
                                <option value="fixed">مبلغ ثابت لكل يوم</option>
                            </select>
                        </div>
                        
                        <div class="mb-3" id="percentageField">
                            <label for="percentageValue" class="form-label">النسبة المئوية (%)</label>
                            <input type="number" class="form-control" id="percentageValue" min="0" max="100" step="0.1">
                        </div>
                        
                        <div class="mb-3" id="fixedField" style="display: none;">
                            <label for="fixedValue" class="form-label">المبلغ الثابت (ريال/يوم)</label>
                            <input type="number" class="form-control" id="fixedValue" min="0" step="0.01">
                        </div>
                        
                        <div class="mb-3">
                            <label for="allowanceIcon" class="form-label">أيقونة البدل</label>
                            <select class="form-select" id="allowanceIcon">
                                <option value="fas fa-money-bill">فاتورة مالية</option>
                                <option value="fas fa-home">سكن</option>
                                <option value="fas fa-car">نقل</option>
                                <option value="fas fa-utensils">طعام</option>
                                <option value="fas fa-shield-alt">أمان</option>
                                <option value="fas fa-graduation-cap">تعليم</option>
                                <option value="fas fa-briefcase">عمل</option>
                                <option value="fas fa-clock">وقت إضافي</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="addNewAllowance()">إضافة البدل</button>
                </div>
            </div>
        </div>
    </div>

    <!-- مكتبة بوتستراب جافاسكريبت -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- ملف الجافاسكريبت المخصص -->
    <script src="assets/js/main.js"></script>
    
    <!-- شريط التنقل السفلي للأجهزة المحمولة -->
    <div class="bottom-nav">
        <a href="index.html" class="bottom-nav-item active">
            <i class="fas fa-home"></i>
            <span>الرئيسية</span>
        </a>
        <a href="scholarship.html" class="bottom-nav-item">
            <i class="fas fa-graduation-cap"></i>
            <span>الابتعاث</span>
        </a>
        <a href="salary-difference.html" class="bottom-nav-item">
            <i class="fas fa-chart-line"></i>
            <span>فرق الراتب</span>
        </a>
        <a href="#" onclick="showAddAllowanceModal()" class="bottom-nav-item">
            <i class="fas fa-plus-circle"></i>
            <span>إضافة</span>
        </a>
    </div>
</body>
</html>






