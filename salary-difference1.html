<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>حاسبة فروقات الراتب</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Google Fonts - Cairo -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #1e3a8a;
            --secondary-color: #64748b;
            --accent-color: #059669;
            --danger-color: #dc2626;
            --light-bg: #f8fafc;
            --border-color: #e2e8f0;
        }

        * {
            font-family: 'Cairo', sans-serif;
        }

        body {
            background: linear-gradient(135deg, var(--light-bg) 0%, #e2e8f0 100%);
            min-height: 100vh;
        }

        .main-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
            color: white;
            padding: 2rem 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid var(--border-color);
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e40af 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(30, 58, 138, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--accent-color) 0%, #047857 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }

        .results-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-top: 2rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .table thead th {
            background: var(--primary-color);
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px;
        }

        .table tbody td {
            padding: 12px 15px;
            border-color: var(--border-color);
        }

        .total-amount {
            background: linear-gradient(135deg, var(--accent-color) 0%, #047857 100%);
            color: white;
            padding: 1.5rem;
            border-radius: 10px;
            text-align: center;
            font-size: 1.5rem;
            font-weight: 700;
            margin-top: 1rem;
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 1.5rem;
            position: relative;
            padding-bottom: 10px;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            right: 0;
            width: 50px;
            height: 3px;
            background: var(--accent-color);
            border-radius: 2px;
        }

        @media print {
            body {
                background: white !important;
                font-size: 12px;
            }
            
            .no-print {
                display: none !important;
            }
            
            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
            }
            
            .main-header {
                background: var(--primary-color) !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .table thead th {
                background: var(--primary-color) !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .total-amount {
                background: var(--accent-color) !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            @page {
                size: A4;
                margin: 1cm;
            }
        }

        .allowance-row {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
        }

        .yearly-salary-row {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
            border-left: 4px solid var(--primary-color);
        }

        .signature-section {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 2px solid var(--border-color);
        }

        .signature-box {
            border: 2px dashed var(--border-color);
            padding: 2rem;
            text-align: center;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .header-logo {
            max-height: 80px;
            max-width: 80px;
            transition: transform 0.3s ease;
        }

        .header-logo:hover {
            transform: scale(1.1);
        }

        .logo-container {
            padding: 10px;
        }

        @media print {
            .header-logo {
                max-height: 60px;
            }
        }

        @media (max-width: 768px) {
            .header-logo {
                max-height: 60px;
                max-width: 60px;
            }
            
            .main-header h1 {
                font-size: 1.5rem;
            }
            
            .main-header .col-md-2 {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- رأس الصفحة -->
    <header class="main-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-2 text-center">
                    <div class="logo-container">
                        <img src="assets/images/ministry-logo2.png" alt="شعار الوزارة" class="header-logo">
                    </div>
                </div>
                <div class="col-md-8 text-center">
                    <h1 class="mb-0">
                        <i class="fas fa-calculator me-3"></i>
                        حاسبة فروقات الراتب
                    </h1>
                    <p class="mb-0 mt-2 opacity-75">للترقيات الاستثنائية</p>
                    <p class="mb-0 mt-1 opacity-75 small">وزارة الدفاع - المملكة العربية السعودية</p>
                </div>
                <div class="col-md-2 text-center">
                    <i class="fas fa-medal fa-3x opacity-75"></i>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-center">
                    <a href="index.html" class="btn btn-light btn-sm">
                        <i class="fas fa-arrow-right me-2"></i>
                        الرجوع للصفحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </header>

    <div class="container my-5">
        <!-- نموذج الإدخال -->
        <div class="card no-print">
            <div class="card-body p-4">
                <h3 class="section-title">
                    <i class="fas fa-user-edit me-2"></i>
                    بيانات العسكري والترقية
                </h3>
                
                <form id="salaryForm">
                    <div class="row">
                        <!-- المعلومات الشخصية -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-user me-2"></i>اسم العسكري
                            </label>
                            <input type="text" class="form-control" id="militaryName" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-id-card me-2"></i>الرقم العسكري
                            </label>
                            <input type="text" class="form-control" id="militaryNumber">
                        </div>
                    </div>

                    <div class="row">
                        <!-- الرتب -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-chevron-down me-2"></i>الرتبة قبل الترقية
                            </label>
                            <input type="text" class="form-control" id="oldRank" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-chevron-up me-2"></i>الرتبة بعد الترقية
                            </label>
                            <input type="text" class="form-control" id="newRank" required>
                        </div>
                    </div>

                    <!-- قسم الرواتب السنوية -->
                    <h4 class="section-title mt-4">
                        <i class="fas fa-chart-line me-2"></i>الرواتب السنوية (حسب الدرجات)
                    </h4>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم إظهار السنوات تلقائً بناءً على تاريخ الترقية والتاريخ الحالي
                    </div>
                    
                    <div id="yearlySalariesContainer">
                        <!-- سيتم إنشاء السنوات تلقائً -->
                    </div>
                    
                    <button type="button" class="btn btn-outline-primary mb-4" onclick="addYearlySalary()">
                        <i class="fas fa-plus me-2"></i>إضافة سنة أخرى
                    </button>

                    <div class="row">
                        <!-- التواريخ -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar me-2"></i>تاريخ الترقية
                            </label>
                            <input type="date" class="form-control" id="promotionDate" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar-check me-2"></i>التاريخ الحالي
                            </label>
                            <input type="date" class="form-control" id="currentDate" required>
                        </div>
                    </div>

                    <!-- قسم البدلات -->
                    <h4 class="section-title mt-4">
                        <i class="fas fa-coins me-2"></i>البدلات
                    </h4>
                    
                    <div id="allowancesContainer">
                        <div class="allowance-row">
                            <div class="row">
                                <div class="col-md-4 mb-2">
                                    <input type="text" class="form-control" placeholder="نوع البدل (مثل: إعاشة، خطر، نقل)">
                                </div>
                                <div class="col-md-3 mb-2">
                                    <input type="number" class="form-control" placeholder="المبلغ القديم">
                                </div>
                                <div class="col-md-3 mb-2">
                                    <input type="number" class="form-control" placeholder="المبلغ الجديد">
                                </div>
                                <div class="col-md-2 mb-2">
                                    <button type="button" class="btn btn-outline-danger btn-sm w-100" onclick="removeAllowance(this)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-outline-primary mb-4" onclick="addAllowance()">
                        <i class="fas fa-plus me-2"></i>إضافة بدل آخر
                    </button>

                    <div class="text-center">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-calculator me-2"></i>حساب الفروقات
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- قسم النتائج -->
        <div id="resultsSection" class="results-section" style="display: none;">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3 class="section-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>تقرير فروقات الراتب والبدلات
                </h3>
                <button class="btn btn-success no-print" onclick="printReport()">
                    <i class="fas fa-print me-2"></i>طباعة التقرير
                </button>
            </div>

            <!-- عرض المعلومات العسكرية -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h5 class="card-title text-primary">بيانات العسكري</h5>
                            <p class="mb-1"><strong>الاسم:</strong> <span id="displayName"></span></p>
                            <p class="mb-1"><strong>الرقم العسكري:</strong> <span id="displayNumber"></span></p>
                            <p class="mb-0"><strong>الترقية:</strong> من <span id="displayOldRank"></span> إلى <span id="displayNewRank"></span></p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h5 class="card-title text-primary">تفاصيل الفترة</h5>
                            <p class="mb-1"><strong>تاريخ الترقية:</strong> <span id="displayPromotionDate"></span></p>
                            <p class="mb-1"><strong>التاريخ الحالي:</strong> <span id="displayCurrentDate"></span></p>
                            <p class="mb-0"><strong>إجمالي الأشهر:</strong> <span id="displayTotalMonths"></span> شهر</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول النتائج -->
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>السنة</th>
                            <th>عدد الأيام</th>
                            <th>فرق الراتب الشهري</th>
                            <th>فرق البدلات الشهري</th>
                            <th>إجمالي الفرق الشهري</th>
                            <th>إجمالي الفرق السنوي</th>
                        </tr>
                    </thead>
                    <tbody id="resultsTableBody">
                    </tbody>
                </table>
            </div>

            <!-- المبلغ الإجمالي -->
            <div class="total-amount">
                <i class="fas fa-coins me-3"></i>
                إجمالي فروقات الراتب والبدلات: <span id="totalDifference">0</span> ريال سعودي
            </div>
        </div>
    </div>

    <!-- مكتبة بوتستراب جافاسكريبت -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // إنشاء حقول الرواتب السنوية بناءً على التواريخ
        function generateYearlySalaries() {
            const promotionDate = document.getElementById('promotionDate').value;
            const currentDate = document.getElementById('currentDate').value;
            
            if (!promotionDate || !currentDate) return;
            
            const startYear = new Date(promotionDate).getFullYear();
            const endYear = new Date(currentDate).getFullYear();
            
            const container = document.getElementById('yearlySalariesContainer');
            container.innerHTML = '';
            
            for (let year = startYear; year <= endYear; year++) {
                const yearRow = document.createElement('div');
                yearRow.className = 'yearly-salary-row';
                yearRow.innerHTML = `
                    <div class="row">
                        <div class="col-md-2 mb-2 d-flex align-items-center">
                            <h6 class="mb-0 text-primary fw-bold">${year}</h6>
                        </div>
                        <div class="col-md-4 mb-2">
                            <input type="number" class="form-control" placeholder="الراتب القديم" data-year="${year}" data-type="old">
                        </div>
                        <div class="col-md-4 mb-2">
                            <input type="number" class="form-control" placeholder="الراتب الجديد" data-year="${year}" data-type="new">
                        </div>
                        <div class="col-md-2 mb-2 d-flex align-items-center">
                            <span class="badge bg-secondary">سنة ${year}</span>
                        </div>
                    </div>
                `;
                container.appendChild(yearRow);
            }
        }

        // الاستماع لتغييرات التواريخ
        document.getElementById('promotionDate').addEventListener('change', generateYearlySalaries);
        document.getElementById('currentDate').addEventListener('change', generateYearlySalaries);

        // تعيين التاريخ الحالي عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('currentDate').value = today;
        });

        // إضافة صف بدل
        function addAllowance() {
            const container = document.getElementById('allowancesContainer');
            const newRow = document.createElement('div');
            newRow.className = 'allowance-row';
            newRow.innerHTML = `
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <input type="text" class="form-control" placeholder="نوع البدل (مثل: إعاشة، خطر، نقل)">
                    </div>
                    <div class="col-md-3 mb-2">
                        <input type="number" class="form-control" placeholder="المبلغ القديم">
                    </div>
                    <div class="col-md-3 mb-2">
                        <input type="number" class="form-control" placeholder="المبلغ الجديد">
                    </div>
                    <div class="col-md-2 mb-2">
                        <button type="button" class="btn btn-outline-danger btn-sm w-100" onclick="removeAllowance(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(newRow);
        }

        // إزالة صف البدل
        function removeAllowance(button) {
            const container = document.getElementById('allowancesContainer');
            if (container.children.length > 1) {
                button.closest('.allowance-row').remove();
            }
        }

        // إضافة صف الراتب السنوي
        function addYearlySalary() {
            const container = document.getElementById('yearlySalariesContainer');
            const newRow = document.createElement('div');
            newRow.className = 'yearly-salary-row';
            newRow.innerHTML = `
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <input type="number" class="form-control" placeholder="السنة (مثل: 2021)">
                    </div>
                    <div class="col-md-3 mb-2">
                        <input type="number" class="form-control" placeholder="الراتب القديم">
                    </div>
                    <div class="col-md-3 mb-2">
                        <input type="number" class="form-control" placeholder="الراتب الجديد">
                    </div>
                    <div class="col-md-3 mb-2">
                        <button type="button" class="btn btn-outline-danger btn-sm w-100" onclick="removeYearlySalary(this)">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
            container.appendChild(newRow);
        }

        // إزالة صف الراتب السنوي
        function removeYearlySalary(button) {
            const container = document.getElementById('yearlySalariesContainer');
            if (container.children.length > 1) {
                button.closest('.yearly-salary-row').remove();
            }
        }

        // حساب الأيام بين تاريخين (30 يومًا في الشهر، 360 يومًا في السنة)
        function calculateDaysDifference(startDate, endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            
            let totalDays = 0;
            let currentDate = new Date(start);
            
            while (currentDate <= end) {
                const currentYear = currentDate.getFullYear();
                const currentMonth = currentDate.getMonth();
                
                // حساب الأيام المتبقية في الشهر الحالي
                const daysInMonth = 30; // Always 30 days per month
                const dayOfMonth = currentDate.getDate();
                const remainingDaysInMonth = daysInMonth - dayOfMonth + 1;
                
                // التحقق إذا كنا في الشهر الأخير
                const nextMonth = new Date(currentYear, currentMonth + 1, 1);
                if (nextMonth > end) {
                    // الشهر الأخير - حساب الأيام بالضبط
                    const endDay = end.getDate();
                    totalDays += Math.min(endDay - dayOfMonth + 1, remainingDaysInMonth);
                    break;
                } else {
                    // شهر كامل
                    totalDays += remainingDaysInMonth;
                    currentDate = nextMonth;
                }
            }
            
            return totalDays;
        }

        // حساب الأيام في سنة محددة
        function calculateDaysInYear(year, startDate, endDate) {
            const yearStart = new Date(Math.max(new Date(startDate).getTime(), new Date(year, 0, 1).getTime()));
            const yearEnd = new Date(Math.min(new Date(endDate).getTime(), new Date(year, 11, 31).getTime()));
            
            if (yearStart > yearEnd) return 0;
            
            return calculateDaysDifference(
                yearStart.toISOString().split('T')[0],
                yearEnd.toISOString().split('T')[0]
            );
        }

        // تنسيق الأرقام بفواصل
        function formatNumber(num) {
            return new Intl.NumberFormat('ar-SA').format(num);
        }

        // تنسيق التاريخ بالعربية
        function formatDateArabic(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        }

        // دالة الحساب الرئيسية
        document.getElementById('salaryForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // الحصول على قيم النموذج
            const militaryName = document.getElementById('militaryName').value;
            const militaryNumber = document.getElementById('militaryNumber').value;
            const oldRank = document.getElementById('oldRank').value;
            const newRank = document.getElementById('newRank').value;
            const promotionDate = document.getElementById('promotionDate').value;
            const currentDate = document.getElementById('currentDate').value;
            
            // الحصول على الرواتب السنوية
            const yearlySalaries = {};
            const salaryInputs = document.querySelectorAll('#yearlySalariesContainer input[data-year]');
            
            salaryInputs.forEach(input => {
                const year = parseInt(input.dataset.year);
                const type = input.dataset.type;
                const value = parseFloat(input.value) || 0;
                
                if (!yearlySalaries[year]) {
                    yearlySalaries[year] = { old: 0, new: 0 };
                }
                
                yearlySalaries[year][type] = value;
                yearlySalaries[year].difference = yearlySalaries[year].new - yearlySalaries[year].old;
            });
            
            // حساب فروق البدلات
            const allowanceRows = document.querySelectorAll('.allowance-row');
            let totalAllowanceDifference = 0;
            
            allowanceRows.forEach(row => {
                const inputs = row.querySelectorAll('input[type="number"]');
                if (inputs.length >= 2) {
                    const oldAmount = parseFloat(inputs[0].value) || 0;
                    const newAmount = parseFloat(inputs[1].value) || 0;
                    totalAllowanceDifference += (newAmount - oldAmount);
                }
            });
            
            // حساب إجمالي الأيام
            const totalDays = calculateDaysDifference(promotionDate, currentDate);
            
            // عرض المعلومات العسكرية
            document.getElementById('displayName').textContent = militaryName;
            document.getElementById('displayNumber').textContent = militaryNumber || 'غير محدد';
            document.getElementById('displayOldRank').textContent = oldRank;
            document.getElementById('displayNewRank').textContent = newRank;
            document.getElementById('displayPromotionDate').textContent = formatDateArabic(promotionDate);
            document.getElementById('displayCurrentDate').textContent = formatDateArabic(currentDate);
            document.getElementById('displayTotalMonths').textContent = Math.round(totalDays / 30) + ' شهر (' + totalDays + ' يوم)';
            
            // إنشاء ملخص سنوي
            const tableBody = document.getElementById('resultsTableBody');
            tableBody.innerHTML = '';
            
            const startDate = new Date(promotionDate);
            const endDate = new Date(currentDate);
            let totalDifference = 0;
            
            let currentYear = startDate.getFullYear();
            
            while (currentYear <= endDate.getFullYear()) {
                const daysInYear = calculateDaysInYear(currentYear, promotionDate, currentDate);
                
                if (daysInYear > 0) {
                    // الحصول على فرق الراتب لهذه السنة
                    const yearSalaryData = yearlySalaries[currentYear];
                    const salaryDifference = yearSalaryData ? yearSalaryData.difference : 0;
                    
                    // حساب المعدلات اليومية
                    const dailySalaryDifference = salaryDifference / 30; // Monthly salary ÷ 30 days
                    const dailyAllowanceDifference = totalAllowanceDifference / 30; // Monthly allowance ÷ 30 days
                    const totalDailyDifference = dailySalaryDifference + dailyAllowanceDifference;
                    
                    // حساب الفروق السنوية
                    const salaryYearlyDifference = dailySalaryDifference * daysInYear;
                    const allowanceYearlyDifference = dailyAllowanceDifference * daysInYear;
                    const totalYearlyDifference = totalDailyDifference * daysInYear;
                    
                    totalDifference += totalYearlyDifference;
                    
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="fw-bold">${currentYear}</td>
                        <td>${daysInYear} يوم</td>
                        <td>${formatNumber(salaryDifference)} ريال</td>
                        <td>${formatNumber(totalAllowanceDifference)} ريال</td>
                        <td class="fw-bold text-primary">${formatNumber(salaryDifference + totalAllowanceDifference)} ريال</td>
                        <td class="fw-bold text-success">${formatNumber(Math.round(totalYearlyDifference))} ريال</td>
                    `;
                    tableBody.appendChild(row);
                }
                
                currentYear++;
            }
            
            // عرض الإجمالي
            document.getElementById('totalDifference').textContent = formatNumber(Math.round(totalDifference));
            
            // إظهار قسم النتائج
            document.getElementById('resultsSection').style.display = 'block';
            document.getElementById('resultsSection').scrollIntoView({ behavior: 'smooth' });
        });

        // دالة الطباعة
        function printReport() {
            // إنشاء نافذة الطباعة
            const printWindow = window.open('', '_blank');
            
            // الحصول على البيانات الحالية
            const militaryName = document.getElementById('displayName').textContent;
            const militaryNumber = document.getElementById('displayNumber').textContent;
            const oldRank = document.getElementById('displayOldRank').textContent;
            const newRank = document.getElementById('displayNewRank').textContent;
            const promotionDate = document.getElementById('displayPromotionDate').textContent;
            const currentDate = document.getElementById('displayCurrentDate').textContent;
            const totalMonths = document.getElementById('displayTotalMonths').textContent;
            const totalDifference = document.getElementById('totalDifference').textContent;
            
            // الحصول على بيانات الجدول
            const tableRows = document.querySelectorAll('#resultsTableBody tr');
            let tableContent = '';
            
            tableRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                tableContent += `
                    <tr>
                        <td>${cells[0].textContent}</td>
                        <td>${cells[1].textContent}</td>
                        <td>${cells[2].textContent}</td>
                        <td>${cells[3].textContent}</td>
                        <td>${cells[4].textContent}</td>
                        <td>${cells[5].textContent}</td>
                    </tr>
                `;
            });
            
            // إنشاء التاريخ والوقت الحاليين
            const now = new Date();
            const printDate = now.toLocaleDateString('ar-SA');
            const printTime = now.toLocaleTimeString('ar-SA');
            
            // إنشاء HTML للطباعة
            const printHTML = `
                <!DOCTYPE html>
                <html lang="ar" dir="rtl">
                <head>
                    <meta charset="UTF-8">
                    <title>تقرير فروقات الراتب - ${militaryName}</title>
                    <style>
                        * { margin: 0; padding: 0; box-sizing: border-box; font-family: Arial, sans-serif; }
                        body { background: white; color: #000; line-height: 1.4; font-size: 12px; }
                        .container { width: 210mm; margin: 0 auto; padding: 15mm; }
                        .header { text-align: center; margin-bottom: 20px; border-bottom: 2px solid #000; padding-bottom: 15px; }
                        .ministry-name { font-size: 16px; font-weight: bold; margin-bottom: 5px; }
                        .report-title { font-size: 18px; font-weight: bold; margin: 10px 0; }
                        .info-section { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0; }
                        .info-box { border: 1px solid #000; padding: 15px; }
                        .info-box h3 { font-size: 14px; font-weight: bold; margin-bottom: 10px; text-align: center; border-bottom: 1px solid #000; padding-bottom: 5px; }
                        .info-row { display: flex; justify-content: space-between; margin-bottom: 8px; padding: 5px 0; border-bottom: 1px dotted #666; }
                        .info-label { font-weight: bold; }
                        .info-value { border: 1px solid #000; padding: 2px 8px; }
                        .table-section { margin: 15px 0; }
                        .section-title { font-size: 14px; font-weight: bold; text-align: center; border: 1px solid #000; padding: 10px; margin-bottom: 10px; }
                        .results-table { width: 100%; border-collapse: collapse; border: 2px solid #000; }
                        .results-table th, .results-table td { border: 1px solid #000; padding: 8px; text-align: center; }
                        .results-table th { background: #f0f0f0; font-weight: bold; }
                        .total-section { border: 2px solid #000; padding: 15px; text-align: center; margin: 15px 0; background: #f5f5f5; }
                        .total-amount { font-size: 20px; font-weight: bold; margin: 8px 0; }
                        .signatures { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px; }
                        .signature-box { border: 1px dashed #000; padding: 15px; text-align: center; height: 80px; }
                        .footer { margin-top: 20px; text-align: center; border-top: 1px solid #000; padding-top: 10px; font-size: 10px; }
                        @page { size: A4; margin: 0; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <div class="ministry-name">المملكة العربية السعودية</div>
                            <div class="ministry-name">وزارة الدفاع</div>
                            <div class="report-title">تقرير فروقات الراتب والبدلات</div>
                        </div>
                        
                        <div class="info-section">
                            <div class="info-box">
                                <h3>بيانات العسكري</h3>
                                <div class="info-row">
                                    <span class="info-label">اسم العسكري:</span>
                                    <span class="info-value">${militaryName}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">الرقم العسكري:</span>
                                    <span class="info-value">${militaryNumber}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">الرتبة السابقة:</span>
                                    <span class="info-value">${oldRank}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">الرتبة الجديدة:</span>
                                    <span class="info-value">${newRank}</span>
                                </div>
                            </div>
                            
                            <div class="info-box">
                                <h3>تفاصيل الفترة</h3>
                                <div class="info-row">
                                    <span class="info-label">تاريخ الترقية:</span>
                                    <span class="info-value">${promotionDate}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">التاريخ الحالي:</span>
                                    <span class="info-value">${currentDate}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">إجمالي المدة:</span>
                                    <span class="info-value">${totalMonths}</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="table-section">
                            <div class="section-title">تفصيل فروقات الراتب والبدلات حسب السنوات</div>
                            <table class="results-table">
                                <thead>
                                    <tr>
                                        <th>السنة</th>
                                        <th>عدد الأيام</th>
                                        <th>فرق الراتب الشهري</th>
                                        <th>فرق البدلات الشهري</th>
                                        <th>إجمالي الفرق الشهري</th>
                                        <th>إجمالي الفرق السنوي</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${tableContent}
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="total-section">
                            <div>إجمالي فروقات الراتب والبدلات المستحقة</div>
                            <div class="total-amount">${totalDifference} ريال سعودي</div>
                        </div>
                        
                        <div class="signatures">
                            <div class="signature-box">
                                <div style="font-weight: bold;">توقيع المحاسب المختص</div>
                                <div style="margin-top: 30px; font-size: 10px;">الاسم: _____________________ التوقيع: _______</div>
                            </div>
                            <div class="signature-box">
                                <div style="font-weight: bold;">توقيع المدقق المالي</div>
                                <div style="margin-top: 30px; font-size: 10px;">الاسم: _____________________ التوقيع: _______</div>
                            </div>
                        </div>
                        
                        <div class="footer">
                            <div>تاريخ الطباعة: ${printDate} - وقت الطباعة: ${printTime}</div>
                        </div>
                    </div>
                </body>
                </html>
            `;
            
            // الكتابة في نافذة الطباعة
            printWindow.document.write(printHTML);
            printWindow.document.close();
            
            // انتظار تحميل المحتوى ثم الطباعة
            printWindow.onload = function() {
                printWindow.print();
                printWindow.close();
            };
        }
    </script>
</body>
</html>

