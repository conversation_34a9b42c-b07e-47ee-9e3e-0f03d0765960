// Global Variables
let allowances = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    loadAllowances();
    updateStatistics();
});

// Load allowances from localStorage
function loadAllowances() {
    const stored = localStorage.getItem('allowances');
    if (stored) {
        allowances = JSON.parse(stored);
    } else {
        // Add default allowances
        allowances = [
            {
                id: 'housing',
                name: 'بدل السكن',
                description: 'بدل السكن الشهري للموظفين',
                type: 'percentage',
                value: 25,
                icon: 'fas fa-home'
            },
            {
                id: 'transport',
                name: 'بدل النقل',
                description: 'بدل النقل اليومي',
                type: 'fixed',
                value: 50,
                icon: 'fas fa-car'
            },
            {
                id: 'food',
                name: 'بدل الطعام',
                description: 'بدل الطعام اليومي',
                type: 'fixed',
                value: 30,
                icon: 'fas fa-utensils'
            }
        ];
        saveAllowances();
    }
    renderAllowances();
}

// Save allowances to localStorage
function saveAllowances() {
    localStorage.setItem('allowances', JSON.stringify(allowances));
}

// Render allowances grid
function renderAllowances() {
    const grid = document.getElementById('allowancesGrid');
    grid.innerHTML = '';
    
    allowances.forEach(allowance => {
        const card = createAllowanceCard(allowance);
        grid.appendChild(card);
    });
    
    updateStatistics();
}

// Create allowance card element
function createAllowanceCard(allowance) {
    const col = document.createElement('div');
    col.className = 'col-md-6 col-lg-4';
    
    const typeClass = allowance.type === 'percentage' ? 'type-percentage' : 'type-fixed';
    const typeText = allowance.type === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت';
    const valueText = allowance.type === 'percentage' ? `${allowance.value}%` : `${allowance.value} ريال/يوم`;
    
    col.innerHTML = `
        <div class="allowance-card" onclick="openAllowancePage('${allowance.id}')">
            <button class="delete-btn" onclick="event.stopPropagation(); deleteAllowance('${allowance.id}')" title="حذف البدل">
                <i class="fas fa-trash"></i>
            </button>
            <button class="edit-btn" onclick="event.stopPropagation(); editAllowance('${allowance.id}')" title="تعديل البدل">
                <i class="fas fa-edit"></i>
            </button>
            <div class="allowance-header">
                <div class="allowance-icon">
                    <i class="${allowance.icon}"></i>
                </div>
                <h4>${allowance.name}</h4>
            </div>
            <div class="allowance-body">
                <span class="allowance-type ${typeClass}">${typeText}</span>
                <p class="text-muted mb-2">${allowance.description}</p>
                <div class="d-flex justify-content-between align-items-center">
                    <strong class="text-primary">${valueText}</strong>
                    <i class="fas fa-arrow-left text-muted"></i>
                </div>
            </div>
        </div>
    `;
    
    return col;
}

// Update statistics
function updateStatistics() {
    const total = allowances.length;
    const percentage = allowances.filter(a => a.type === 'percentage').length;
    const fixed = allowances.filter(a => a.type === 'fixed').length;
    
    document.getElementById('totalAllowances').textContent = total;
    document.getElementById('percentageAllowances').textContent = percentage;
    document.getElementById('fixedAllowances').textContent = fixed;
}

// Show add allowance modal
function showAddAllowanceModal() {
    const modal = new bootstrap.Modal(document.getElementById('addAllowanceModal'));
    document.getElementById('addAllowanceForm').reset();
    toggleCalculationFields();
    modal.show();
}

// Calculate daily amount from monthly amount
function calculateDailyAmount() {
    const monthlyAmount = parseFloat(document.getElementById('monthlyAmount').value) || 0;
    const dailyAmount = monthlyAmount / 30;
    document.getElementById('fixedValue').value = dailyAmount;
}

// Toggle calculation fields based on type
function toggleCalculationFields() {
    const type = document.getElementById('calculationType').value;
    const percentageField = document.getElementById('percentageField');
    const fixedField = document.getElementById('fixedField');
    
    if (type === 'percentage') {
        percentageField.style.display = 'block';
        fixedField.style.display = 'none';
        // Clear fixed field values when switching
        document.getElementById('monthlyAmount').value = '';
        document.getElementById('fixedValue').value = '';
    } else {
        percentageField.style.display = 'none';
        fixedField.style.display = 'block';
        // Clear percentage field when switching
        document.getElementById('percentageValue').value = '';
    }
}

// Add new allowance
function addNewAllowance() {
    const form = document.getElementById('addAllowanceForm');
    const formData = new FormData(form);
    
    const name = document.getElementById('allowanceName').value.trim();
    const description = document.getElementById('allowanceDescription').value.trim();
    const type = document.getElementById('calculationType').value;
    const icon = document.getElementById('allowanceIcon').value;
    
    if (!name) {
        alert('يرجى إدخال اسم البدل');
        return;
    }
    
    let value;
    if (type === 'percentage') {
        value = parseFloat(document.getElementById('percentageValue').value);
        if (!value || value <= 0 || value > 100) {
            alert('يرجى إدخال نسبة مئوية صحيحة (1-100)');
            return;
        }
    } else {
        value = parseFloat(document.getElementById('fixedValue').value);
        if (!value || value <= 0) {
            alert('يرجى إدخال مبلغ ثابت صحيح');
            return;
        }
    }
    
    // Generate unique ID using timestamp to avoid conflicts
    const timestamp = Date.now();
    const baseId = name.toLowerCase().replace(/\s+/g, '-').replace(/[^\w\-]/g, '');
    const id = `${baseId}-${timestamp}`;
    
    // Check if allowance already exists by name (not ID)
    if (allowances.find(a => a.name.toLowerCase().trim() === name.toLowerCase().trim())) {
        alert('يوجد بدل بنفس الاسم مسبق');
        return;
    }
    
    const newAllowance = {
        id,
        name,
        description: description || 'لا يوجد وصف',
        type,
        value,
        icon
    };
    
    allowances.push(newAllowance);
    saveAllowances();
    renderAllowances();
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('addAllowanceModal'));
    modal.hide();
    
    // Show success message
    showToast('تم إضافة البدل بنجاح', 'success');
}

// Delete allowance
function deleteAllowance(id) {
    if (confirm('هل أنت متأكد من حذف هذا البدل؟')) {
        allowances = allowances.filter(a => a.id !== id);
        saveAllowances();
        renderAllowances();
        showToast('تم حذف البدل بنجاح', 'success');
    }
}

// Open allowance calculation page
function openAllowancePage(id) {
    window.location.href = `allowance.html?id=${id}`;
}

// Show toast notification
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    
    // Add to page
    let container = document.getElementById('toastContainer');
    if (!container) {
        container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(container);
    }
    
    container.appendChild(toast);
    
    // Show toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
    
    // Remove after hiding
    toast.addEventListener('hidden.bs.toast', () => {
        toast.remove();
    });
}

// Edit allowance function
function editAllowance(id) {
    const allowance = allowances.find(a => a.id === id);
    if (!allowance) {
        alert('البدل المطلوب غير موجود');
        return;
    }

    // Fill the form with allowance data
    document.getElementById('allowanceName').value = allowance.name;
    document.getElementById('allowanceDescription').value = allowance.description;
    document.getElementById('calculationType').value = allowance.type;
    document.getElementById('allowanceIcon').value = allowance.icon;

    if (allowance.type === 'percentage') {
        document.getElementById('percentageValue').value = allowance.value;
    } else {
        document.getElementById('fixedValue').value = allowance.value;
    }

    toggleCalculationFields();

    // Change the modal title
    document.querySelector('#addAllowanceModal .modal-title').textContent = 'تعديل البدل';
    
    // Change the add button to update button
    const addButton = document.querySelector('#addAllowanceModal .modal-footer .btn-primary');
    addButton.textContent = 'تحديث البدل';
    addButton.setAttribute('onclick', `updateAllowance('${id}')`);

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('addAllowanceModal'));
    modal.show();
}

// Update allowance function
function updateAllowance(id) {
    const form = document.getElementById('addAllowanceForm');
    
    const name = document.getElementById('allowanceName').value.trim();
    const description = document.getElementById('allowanceDescription').value.trim();
    const type = document.getElementById('calculationType').value;
    const icon = document.getElementById('allowanceIcon').value;

    if (!name) {
        alert('يرجى إدخال اسم البدل');
        return;
    }

    let value;
    if (type === 'percentage') {
        value = parseFloat(document.getElementById('percentageValue').value);
        if (!value || value <= 0 || value > 100) {
            alert('يرجى إدخال نسبة مئوية صحيحة (1-100)');
            return;
        }
    } else {
        value = parseFloat(document.getElementById('fixedValue').value);
        if (!value || value <= 0) {
            alert('يرجى إدخال مبلغ ثابت صحيح');
            return;
        }
    }

    // Find the allowance index
    const allowanceIndex = allowances.findIndex(a => a.id === id);
    if (allowanceIndex === -1) {
        alert('البدل المطلوب غير موجود');
        return;
    }

    // Update allowance data
    allowances[allowanceIndex] = {
        id,
        name,
        description: description || 'لا يوجد وصف',
        type,
        value,
        icon
    };

    saveAllowances();
    renderAllowances();

    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('addAllowanceModal'));
    modal.hide();

    // Reset modal
    document.querySelector('#addAllowanceModal .modal-title').textContent = 'إضافة بدل جديد';
    const addButton = document.querySelector('#addAllowanceModal .modal-footer .btn-primary');
    addButton.textContent = 'إضافة البدل';
    addButton.setAttribute('onclick', 'addNewAllowance()');
    document.getElementById('addAllowanceForm').reset();

    // Show success message
    showToast('تم تحديث البدل بنجاح', 'success');
}

// Utility function to get allowance by ID
function getAllowanceById(id) {
    return allowances.find(a => a.id === id);
}
