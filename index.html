<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام حساب البدلات</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.html">
                <i class="fas fa-calculator me-2"></i>
                نظام حساب البدلات
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="index.html">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="scholarship.html">بدل الابتعاث</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showAddAllowanceModal()">إضافة بدل جديد</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- المحتوى الرئيسي -->
    <div class="container mt-4">
        <!-- قسم الترحيب -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="welcome-card">
                    <div class="text-center mb-4">
                        <img src="assets/images/ministry-logo2.png" alt="شعار وزارة الدفاع" class="ministry-logo">
                    </div>
                    <h1 class="display-9 text-center mb-3">
                        <i class="fas fa-coins financial-icon me-3"></i>
                        نظام حساب البدلات والعلاوات
                        <i class="fas fa-chart-line financial-icon ms-3"></i>
                    </h1>
                    <h2 class="h4 text-center mb-3 text-muted">
                        <i class="fas fa-shield-alt me-2"></i>
                        فرع الشؤون الإدارية والمالية للقوات البرية بالجنوبية
                    </h2>
                    <!-- أيقونات النقود المتحركة -->
                    <div class="money-rain" style="left: 10%; animation-delay: 0s;">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="money-rain" style="left: 20%; animation-delay: 1s;">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="money-rain" style="left: 80%; animation-delay: 2s;">
                        <i class="fas fa-money-bill"></i>
                    </div>
                    <div class="money-rain" style="left: 90%; animation-delay: 3s;">
                        <i class="fas fa-coins"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقات الإحصائيات -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-icon bg-primary">
                        <i class="fas fa-list"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalAllowances">0</h3>
                        <p>إجمالي البدلات</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-icon bg-success">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="percentageAllowances">0</h3>
                        <p>بدلات نسبية</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-card">
                    <div class="stat-icon bg-info">
                        <i class="fas fa-coins"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="fixedAllowances">0</h3>
                        <p>بدلات ثابتة</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- زر إضافة بدل جديد -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <a href="scholarship.html" class="btn btn-warning btn-lg me-3 mb-2">
                    <i class="fas fa-graduation-cap me-2"></i>
                    حساب بدل الابتعاث
                </a>
                <a href="salary-difference.html" class="btn btn-info btn-lg me-3 mb-2">
                    <i class="fas fa-chart-line me-2"></i>
                    حساب فرق الراتب
                </a>
                <button class="btn btn-success btn-lg mb-2" onclick="showAddAllowanceModal()">
                    <i class="fas fa-plus me-2"></i>
                    إضافة بدل جديد
                </button>
            </div>
        </div>

        <!-- شبكة البدلات -->
        <div class="row" id="allowancesGrid">
            <!-- سيتم تحميل البدلات هنا -->
        </div>
    </div>

    <!-- نافذة إضافة البدل -->
    <div class="modal fade" id="addAllowanceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة بدل جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addAllowanceForm">
                        <div class="mb-3">
                            <label for="allowanceName" class="form-label">اسم البدل</label>
                            <input type="text" class="form-control" id="allowanceName" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="allowanceDescription" class="form-label">وصف البدل</label>
                            <textarea class="form-control" id="allowanceDescription" rows="2"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="calculationType" class="form-label">طريقة الحساب</label>
                            <select class="form-select" id="calculationType" onchange="toggleCalculationFields()">
                                <option value="percentage">نسبة مئوية من الراتب</option>
                                <option value="fixed">مبلغ ثابت لكل يوم</option>
                            </select>
                        </div>
                        
                        <div class="mb-3" id="percentageField">
                            <label for="percentageValue" class="form-label">النسبة المئوية (%)</label>
                            <input type="number" class="form-control" id="percentageValue" min="0" max="100" step="0.1">
                        </div>
                        
                        <div class="mb-3" id="fixedField" style="display: none;">
                            <label for="monthlyAmount" class="form-label">المبلغ الشهري (ريال)</label>
                            <input type="number" class="form-control" id="monthlyAmount" min="0" step="0.01" placeholder="أدخل المبلغ الشهري" oninput="calculateDailyAmount()">
                            <div class="form-text">سيتم تقسيم المبلغ على 30 يوم تلقائياً</div>
                            
                            <label for="fixedValue" class="form-label mt-3">المبلغ الثابت (ريال/يوم)</label>
                            <input type="number" class="form-control" id="fixedValue" min="0" step="0.01" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label for="allowanceIcon" class="form-label">أيقونة البدل</label>
                            <select class="form-select" id="allowanceIcon">
                                <option value="fas fa-money-bill">فاتورة مالية</option>
                                <option value="fas fa-home">سكن</option>
                                <option value="fas fa-car">نقل</option>
                                <option value="fas fa-utensils">طعام</option>
                                <option value="fas fa-shield-alt">أمان</option>
                                <option value="fas fa-graduation-cap">تعليم</option>
                                <option value="fas fa-briefcase">عمل</option>
                                <option value="fas fa-clock">وقت إضافي</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="addNewAllowance()">إضافة البدل</button>
                </div>
            </div>
        </div>
    </div>

    <!-- مكتبة بوتستراب جافاسكريبت -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- ملف الجافاسكريبت المخصص -->
    <script src="assets/js/main.js"></script>
</body>
</html>






