// Global variables
let currentAllowance = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, initializing allowance page...');

    // Load allowance data first
    loadAllowanceData();

    // Setup event listeners
    setupEventListeners();

    // Add a simple test to verify everything is working
    setTimeout(() => {
        console.log('Testing after initialization...');
        console.log('Current allowance:', currentAllowance);
        console.log('Calculate function available:', typeof window.calculateAllowance);
        console.log('Test function available:', typeof window.testYearlyFields);

        // Test button availability
        const calculateBtn = document.getElementById('calculateBtn');
        const testBtn = document.getElementById('testBtn');
        console.log('Buttons found:', {
            calculate: !!calculateBtn,
            test: !!testBtn
        });

    }, 1000);
});

// Load allowance data from URL parameter
function loadAllowanceData() {
    const urlParams = new URLSearchParams(window.location.search);
    const allowanceId = urlParams.get('id');

    if (!allowanceId) {
        window.location.href = 'index.html';
        return;
    }

    // Get allowances from localStorage
    const allowances = JSON.parse(localStorage.getItem('allowances') || '[]');
    currentAllowance = allowances.find(a => a.id === allowanceId);

    if (!currentAllowance) {
        alert('البدل المطلوب غير موجود');
        window.location.href = 'index.html';
        return;
    }

    console.log('Loaded allowance:', currentAllowance); // للتأكد من التحميل
    renderAllowanceHeader();
}

// Render allowance header
function renderAllowanceHeader() {
    const header = document.getElementById('allowanceHeader');
    const typeText = currentAllowance.type === 'percentage' ? 'نسبة مئوية' : 'مبلغ ثابت';
    const valueText = currentAllowance.type === 'percentage'
        ? `${currentAllowance.value}%`
        : `${currentAllowance.value} ريال/يوم`;

    header.innerHTML = `
        <div class="allowance-icon-large">
            <i class="${currentAllowance.icon}"></i>
        </div>
        <h2>${currentAllowance.name}</h2>
        <p class="lead mb-3">${currentAllowance.description}</p>
        <div class="row">
            <div class="col-md-6">
                <div class="text-center">
                    <small>نوع الحساب</small>
                    <div class="h5">${typeText}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="text-center">
                    <small>القيمة</small>
                    <div class="h5">${valueText}</div>
                </div>
            </div>
        </div>
    `;
}

// Setup event listeners
function setupEventListeners() {
    console.log('Setting up event listeners...');

    // Auto-calculate when inputs change
    const inputs = ['startDate', 'endDate', 'currentSalary', 'previousSalary'];
    inputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('change', handleInputChange);
            element.addEventListener('input', handleInputChange);
            console.log(`Added listeners to ${id}`);
        }
    });

    // Add button event listeners using IDs
    const calculateBtn = document.getElementById('calculateBtn');
    if (calculateBtn) {
        calculateBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Calculate button clicked');
            calculateAllowance();
        });
        console.log('Added calculate button listener');
    }

    const testBtn = document.getElementById('testBtn');
    if (testBtn) {
        testBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('Test button clicked');
            testYearlyFields();
        });
        console.log('Added test button listener');
    }

    // Set default dates
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');

    if (startDateEl) startDateEl.value = formatDate(firstDayOfMonth);
    if (endDateEl) endDateEl.value = formatDate(today);

    console.log('Event listeners setup complete');
}

// Handle input changes
function handleInputChange(event) {
    // Don't trigger field regeneration if the change is from a yearly salary input
    const isYearlySalaryInput = event && event.target && event.target.classList.contains('yearly-salary-input');

    if (!isYearlySalaryInput) {
        // Check if we need to show yearly salary fields
        checkAndShowYearlySalaryFields();
    }

    // Auto calculate with debounce
    clearTimeout(window.calculateTimeout);
    window.calculateTimeout = setTimeout(() => {
        if (isFormValid()) {
            calculateAllowance();
        }
    }, 500);
}

// Check if we need yearly salary fields
function checkAndShowYearlySalaryFields() {
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');

    if (!startDateEl || !endDateEl || !currentAllowance) return;

    const startDate = startDateEl.value;
    const endDate = endDateEl.value;

    if (!startDate || !endDate) return;

    const isCurrentSalaryBased = currentAllowance.description === 'من المربوط الحالي';
    const yearlySalariesContainer = document.getElementById('yearlySalariesContainer');
    const previousSalaryField = document.getElementById('previousSalaryField');

    console.log('Checking yearly fields:', {
        isCurrentSalaryBased,
        startDate,
        endDate,
        allowanceDescription: currentAllowance.description
    });

    if (isCurrentSalaryBased) {
        const startYear = new Date(startDate).getFullYear();
        const endYear = new Date(endDate).getFullYear();
        const yearsDiff = endYear - startYear + 1;

        console.log('Years difference:', yearsDiff);

        if (yearsDiff > 1) {
            // Multiple years - show yearly salary fields
            const years = [];
            for (let year = startYear; year <= endYear; year++) {
                years.push(year);
            }

            generateYearlySalaryFields(years);
            if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'block';
            if (previousSalaryField) previousSalaryField.style.display = 'none';
        } else {
            // Single year - hide yearly fields
            if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'none';
            if (previousSalaryField) previousSalaryField.style.display = 'block';
        }
    } else {
        // Normal calculation - show previous salary field
        if (yearlySalariesContainer) yearlySalariesContainer.style.display = 'none';
        if (previousSalaryField) previousSalaryField.style.display = 'block';
    }
}

// Generate yearly salary fields
function generateYearlySalaryFields(years) {
    const container = document.getElementById('yearlySalariesFields');
    if (!container) {
        console.error('yearlySalariesFields container not found');
        return;
    }

    console.log('Generating fields for years:', years);

    // Check if fields already exist for these years
    const existingFields = container.querySelectorAll('.yearly-salary-field');
    const existingYears = Array.from(existingFields).map(field => {
        const input = field.querySelector('input');
        return input ? parseInt(input.dataset.year) : null;
    }).filter(year => year !== null);

    // If the same years already exist, don't regenerate
    if (existingYears.length === years.length &&
        years.every(year => existingYears.includes(year))) {
        console.log('Fields already exist for these years, skipping generation');
        return;
    }

    // Store existing values before clearing
    const existingValues = {};
    existingFields.forEach(field => {
        const input = field.querySelector('input');
        if (input && input.value) {
            existingValues[input.dataset.year] = input.value;
        }
    });

    // Clear existing fields
    container.innerHTML = '';

    const currentYear = new Date().getFullYear();
    const currentSalaryEl = document.getElementById('currentSalary');
    const currentSalaryValue = currentSalaryEl ? currentSalaryEl.value : '';

    years.forEach((year, index) => {
        const fieldDiv = document.createElement('div');
        fieldDiv.className = 'mb-3 yearly-salary-field';

        let yearLabel = `راتب سنة ${year}`;
        let placeholder = `أدخل راتب سنة ${year}`;
        let defaultValue = '';

        // Restore existing value if available
        if (existingValues[year]) {
            defaultValue = existingValues[year];
        } else if (year === currentYear) {
            yearLabel += ' (السنة الحالية)';
            defaultValue = currentSalaryValue;
        } else if (year < currentYear) {
            yearLabel += ' (سنة سابقة)';
        } else {
            yearLabel += ' (سنة مستقبلية)';
        }

        fieldDiv.innerHTML = `
            <label for="salary_${year}" class="form-label">
                <i class="fas fa-calendar-year me-2"></i>
                ${yearLabel}
            </label>
            <input type="number"
                   class="form-control yearly-salary-input"
                   id="salary_${year}"
                   data-year="${year}"
                   placeholder="${placeholder}"
                   min="0"
                   step="0.01"
                   value="${defaultValue}">
        `;

        container.appendChild(fieldDiv);

        // Add event listener to the new field
        const input = fieldDiv.querySelector('input');
        if (input) {
            input.addEventListener('change', handleInputChange);
            input.addEventListener('input', handleInputChange);
        }
    });

    console.log(`Generated ${years.length} yearly salary fields`);
}

// Auto calculate with debounce
function autoCalculate() {
    clearTimeout(calculateTimeout);
    calculateTimeout = setTimeout(() => {
        if (isFormValid()) {
            calculateAllowance();
        }
    }, 500);
}

// Check if form is valid - Enhanced
function isFormValid() {
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    const currentSalaryEl = document.getElementById('currentSalary');

    if (!startDateEl || !endDateEl) {
        console.log('Required form elements not found');
        return false;
    }

    const startDate = startDateEl.value;
    const endDate = endDateEl.value;
    
    // Only check current salary if allowance is not fixed type
    let currentSalary = '';
    if (currentAllowance && currentAllowance.type !== 'fixed') {
        if (!currentSalaryEl) {
            console.log('Current salary element not found');
            return false;
        }
        currentSalary = currentSalaryEl.value;
        if (!currentSalary || parseFloat(currentSalary) <= 0) {
            console.log('Current salary validation failed');
            return false;
        }
    }

    console.log('Validating form:', { startDate, endDate, currentSalary, allowanceType: currentAllowance?.type });

    if (!startDate || !endDate) {
        console.log('Basic validation failed');
        return false;
    }

    // Check if currentAllowance is loaded
    if (!currentAllowance) {
        console.log('Current allowance not loaded');
        return false;
    }

    return true;
}

// Make functions globally available
window.calculateAllowance = calculateAllowance;
window.printResults = printResults;
window.exportToPDF = exportToPDF;

// Test yearly fields function
window.testYearlyFields = function() {
    console.log("Testing yearly fields function...");

    // Check if we need to show yearly salary fields
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');

    if (!startDateEl || !endDateEl) {
        console.error("Date fields not found");
        return;
    }

    // Set some test dates (multi-year period)
    const today = new Date();
    const lastYear = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());

    startDateEl.value = formatDate(lastYear);
    endDateEl.value = formatDate(today);

    // Trigger change event to show yearly fields
    if (typeof handleInputChange === 'function') {
        handleInputChange({ target: startDateEl });
    }

    alert("تم تعيين تواريخ اختبار لفترة متعددة السنوات. يجب أن تظهر حقول الرواتب السنوية الآن.");
};

// Calculate allowance function
function calculateAllowance() {
    console.log('Calculate button clicked');

    if (!isFormValid()) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // Show calculating state
    document.body.classList.add('calculating');

    setTimeout(() => {
        try {
            const result = performCalculation();
            displayResults(result);
            displayPeriodDetails(result.periodDetails);
        } catch (error) {
            console.error('Calculation error:', error);
            alert('حدث خطأ في الحساب: ' + error.message);
        } finally {
            document.body.classList.remove('calculating');
        }
    }, 500);
}

// Perform the actual calculation - Fixed
function performCalculation() {
    // Get elements safely
    const startDateEl = document.getElementById('startDate');
    const endDateEl = document.getElementById('endDate');
    const currentSalaryEl = document.getElementById('currentSalary');
    const previousSalaryEl = document.getElementById('previousSalary');

    // Check if required elements exist
    if (!startDateEl || !endDateEl) {
        throw new Error('عناصر النموذج المطلوبة غير موجودة');
    }

    // Get values safely
    const startDateValue = startDateEl.value;
    const endDateValue = endDateEl.value;
    
    // Only get salary values if not fixed type
    let currentSalaryValue = '';
    let previousSalaryValue = '';
    
    if (currentAllowance && currentAllowance.type !== 'fixed') {
        if (!currentSalaryEl) {
            throw new Error('حقل الراتب الحالي مطلوب');
        }
        currentSalaryValue = currentSalaryEl.value;
        previousSalaryValue = previousSalaryEl ? previousSalaryEl.value : '';
        
        if (!currentSalaryValue) {
            throw new Error('يرجى ملء حقل الراتب الحالي');
        }
    }

    // Validate values
    if (!startDateValue || !endDateValue) {
        throw new Error('يرجى ملء جميع الحقول المطلوبة');
    }

    const startDate = new Date(startDateValue);
    const endDate = new Date(endDateValue);
    
    // Only parse salary if not fixed type
    let currentSalary = 0;
    let previousSalary = 0;
    
    if (currentAllowance && currentAllowance.type !== 'fixed') {
        currentSalary = parseFloat(currentSalaryValue);
        previousSalary = parseFloat(previousSalaryValue) || currentSalary;
        
        // Validate salary
        if (isNaN(currentSalary) || currentSalary <= 0) {
            throw new Error('يرجى إدخال راتب صحيح');
        }
    }

    // Validate dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        throw new Error('تواريخ غير صحيحة');
    }

    if (endDate <= startDate) {
        throw new Error('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
    }

    // Check if currentAllowance exists
    if (!currentAllowance) {
        throw new Error('لم يتم تحديد نوع البدل');
    }

    // Calculate period details
    const periodDetails = calculatePeriodDetails(startDate, endDate);

    // Check if this is "من المربوط الحالي" calculation
    const isCurrentSalaryBased = currentAllowance.description === 'من المربوط الحالي';

    // Get yearly salaries if needed
    const yearlySalaries = {};
    if (isCurrentSalaryBased && periodDetails.years.length > 1) {
        let hasEmptyFields = false;

        periodDetails.years.forEach(yearData => {
            const salaryInput = document.getElementById(`salary_${yearData.year}`);
            if (salaryInput) {
                const salary = parseFloat(salaryInput.value);
                if (!salary || salary <= 0) {
                    hasEmptyFields = true;
                } else {
                    yearlySalaries[yearData.year] = salary;
                }
            } else {
                hasEmptyFields = true;
            }
        });

        if (hasEmptyFields) {
            throw new Error('يرجى إدخال راتب صحيح لجميع السنوات');
        }
    }

    // Calculate allowance for each year
    let totalAllowance = 0;
    const yearlyCalculations = [];

    periodDetails.years.forEach(yearData => {
        let yearSalary;

        if (isCurrentSalaryBased && yearlySalaries[yearData.year]) {
            yearSalary = yearlySalaries[yearData.year];
        } else if (yearData.year === new Date().getFullYear()) {
            yearSalary = currentSalary;
        } else {
            yearSalary = previousSalary;
        }

        let yearAllowance = 0;

        if (currentAllowance.type === 'percentage') {
            const dailyRate = (yearSalary * (currentAllowance.value / 100)) / 30;
            yearAllowance = dailyRate * yearData.days;
        } else {
            yearAllowance = currentAllowance.value * yearData.days;
        }

        totalAllowance += yearAllowance;

        yearlyCalculations.push({
            year: yearData.year,
            days: yearData.days,
            salary: yearSalary,
            allowance: yearAllowance,
            isCurrentSalaryBased: isCurrentSalaryBased
        });
    });

    return {
        totalAllowance,
        totalDays: periodDetails.totalDays,
        yearlyCalculations,
        periodDetails,
        allowanceInfo: currentAllowance
    };
}

// Calculate period details (30 days per month, 360 days per year)
function calculatePeriodDetails(startDate, endDate) {
    const startYear = startDate.getFullYear();
    const startMonth = startDate.getMonth() + 1;
    const startDay = startDate.getDate();

    const endYear = endDate.getFullYear();
    const endMonth = endDate.getMonth() + 1;
    const endDay = endDate.getDate();

    // Calculate total days using 30-day month system
    const totalDays = calculateDaysBetween(startDate, endDate);

    // Get years in the period
    const years = [];
    for (let year = startYear; year <= endYear; year++) {
        const yearStartDate = year === startYear ? startDate : new Date(year, 0, 1);
        const yearEndDate = year === endYear ? endDate : new Date(year, 11, 30);

        const yearDays = calculateDaysBetween(yearStartDate, yearEndDate);

        years.push({
            year: year,
            days: yearDays,
            startDate: yearStartDate,
            endDate: yearEndDate
        });
    }

    return {
        totalDays: totalDays,
        years: years,
        startDate: startDate,
        endDate: endDate
    };
}

// Calculate days between two dates (30 days per month system)
function calculateDaysBetween(start, end) {
    const startYear = start.getFullYear();
    const startMonth = start.getMonth() + 1; // Convert to 1-12
    const startDay = start.getDate();

    const endYear = end.getFullYear();
    const endMonth = end.getMonth() + 1; // Convert to 1-12
    const endDay = end.getDate();

    // Ensure days don't exceed 30 in our system
    const adjustedStartDay = Math.min(startDay, 30);
    const adjustedEndDay = Math.min(endDay, 30);

    // Calculate total months
    const totalStartMonths = (startYear * 12) + startMonth;
    const totalEndMonths = (endYear * 12) + endMonth;

    let totalDays = 0;

    if (totalStartMonths === totalEndMonths) {
        // Same month
        totalDays = adjustedEndDay - adjustedStartDay + 1;
    } else {
        // Different months
        // Days remaining in start month
        totalDays += (30 - adjustedStartDay + 1);

        // Full months between
        const monthsBetween = totalEndMonths - totalStartMonths - 1;
        totalDays += monthsBetween * 30;

        // Days in end month
        totalDays += adjustedEndDay;
    }

    return Math.max(0, totalDays);
}

// Display calculation results
function displayResults(result) {
    const resultsCard = document.getElementById('resultsCard');
    const resultsContent = document.getElementById('resultsContent');

    let html = '';

    // Yearly breakdown if multiple years
    if (result.yearlyCalculations.length > 1) {
        html += '<h6 class="mb-3">التفصيل السنوي:</h6>';
        result.yearlyCalculations.forEach(calc => {
            html += `
                <div class="result-item">
                    <div class="result-label">سنة ${calc.year} (${calc.days} يوم)</div>
                    <div class="result-value">${formatCurrency(calc.allowance)}</div>
                    <div class="result-details">
                        الراتب: ${formatCurrency(calc.salary)}
                        ${calc.isCurrentSalaryBased ? ' (من المربوط الحالي)' : ''}
                    </div>
                </div>
            `;
        });

        html += '<hr class="my-3">';
    }

    // Total result
    html += `
        <div class="total-result">
            <div class="total-label">إجمالي البدل</div>
            <div class="total-value">${formatCurrency(result.totalAllowance)}</div>
            <div class="total-details">
                ${result.allowanceInfo.name} - ${result.totalDays} يوم
            </div>
        </div>
    `;

    resultsContent.innerHTML = html;
    resultsCard.style.display = 'block';

    // Scroll to results
    resultsCard.scrollIntoView({ behavior: 'smooth' });
}

// Display period details
function displayPeriodDetails(periodDetails) {
    const detailsCard = document.getElementById('periodDetails');
    const detailsContent = document.getElementById('periodDetailsContent');

    const html = `
        <div class="period-item">
            <span class="period-label">تاريخ البداية</span>
            <span class="period-value">${formatDateArabic(periodDetails.startDate)}</span>
        </div>
        <div class="period-item">
            <span class="period-label">تاريخ النهاية</span>
            <span class="period-value">${formatDateArabic(periodDetails.endDate)}</span>
        </div>
        <div class="period-item">
            <span class="period-label">إجمالي الأيام</span>
            <span class="period-value">${periodDetails.totalDays} يوم</span>
        </div>
        <div class="period-item">
            <span class="period-label">عدد السنوات</span>
            <span class="period-value">${periodDetails.years.length} سنة</span>
        </div>
        <div class="period-item">
            <span class="period-label">طريقة الحساب</span>
            <span class="period-value">30 يوم لكل شهر</span>
        </div>
    `;

    detailsContent.innerHTML = html;
    detailsCard.style.display = 'block';
}

// Utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount);
}

function formatDate(date) {
    return date.toISOString().split('T')[0];
}

function formatDateArabic(date) {
    return new Intl.DateTimeFormat('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(date);
}

// Print results
function printResults() {
    window.print();
}

// Export to PDF
function exportToPDF() {
    if (!validateFormForReport()) {
        return;
    }

    // Validate form function
    function validateFormForReport() {
        const startDate = document.getElementById('startDate');
        const endDate = document.getElementById('endDate');
        const currentSalary = document.getElementById('currentSalary');

        if (!startDate || !endDate || !currentSalary) {
            alert('يرجى التأكد من تعبئة جميع الحقول');
            return false;
        }

        if (!startDate.value || !endDate.value || !currentSalary.value) {
            alert('يرجى تعبئة جميع الحقول المطلوبة');
            return false;
        }

        return true;
    }

    if (typeof jsPDF === 'undefined') {
        alert('مكتبة PDF غير متوفرة');
        return;
    }

    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    doc.setFont('helvetica');
    doc.setFontSize(16);

    doc.text('تقرير حساب بدل', 105, 20, { align: 'center' });

    doc.save('تقرير-بدل.pdf');
}
