/* Arabic Font and RTL Support */
* {
    font-family: 'Cairo', sans-serif;
}

body {
    background: linear-gradient(135deg, #2d3436 0%, #636e72 50%, #74b9ff 100%);
    min-height: 100vh;
    direction: rtl;
    position: relative;
    overflow-x: hidden;
}

/* Military Color Scheme */
:root {
    --military-dark: #2d3436;
    --military-olive: #6c5ce7;
    --military-green: #00b894;
    --military-light-green: #55efc4;
    --military-white: #ffffff;
    --military-gray: #636e72;
    --military-gold: #fdcb6e;
    --shadow-color: rgba(45, 52, 54, 0.3);
}

/* Animated Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 80%, rgba(0, 184, 148, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(108, 92, 231, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(85, 239, 196, 0.05) 0%, transparent 50%);
    animation: backgroundMove 20s ease-in-out infinite;
    z-index: -1;
}

@keyframes backgroundMove {
    0%, 100% { transform: translateX(0) translateY(0); }
    25% { transform: translateX(-20px) translateY(-10px); }
    50% { transform: translateX(20px) translateY(10px); }
    75% { transform: translateX(-10px) translateY(20px); }
}

/* Welcome Card */
.welcome-card {
    background: linear-gradient(145deg, var(--military-white), #f8f9fa);
    padding: 3rem 2rem;
    border-radius: 20px;
    box-shadow: 
        0 20px 40px var(--shadow-color),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    margin-bottom: 2rem;
    border: 1px solid rgba(108, 92, 231, 0.1);
    position: relative;
    overflow: hidden;
}

.welcome-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--military-olive), var(--military-green), var(--military-light-green));
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Financial Icons Animation */
.financial-icon {
    display: inline-block;
    animation: float 3s ease-in-out infinite;
    color: var(--military-gold);
    filter: drop-shadow(0 4px 8px rgba(253, 203, 110, 0.3));
}

.financial-icon:nth-child(2) { animation-delay: 0.5s; }
.financial-icon:nth-child(3) { animation-delay: 1s; }

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-10px) rotate(2deg); }
    50% { transform: translateY(-5px) rotate(-1deg); }
    75% { transform: translateY(-15px) rotate(1deg); }
}

/* Money Animation */
.money-rain {
    position: absolute;
    top: -10px;
    animation: moneyFall 4s linear infinite;
    color: var(--military-gold);
    opacity: 0.6;
    font-size: 1.2rem;
}

@keyframes moneyFall {
    0% { transform: translateY(-100px) rotate(0deg); opacity: 0; }
    10% { opacity: 0.6; }
    90% { opacity: 0.6; }
    100% { transform: translateY(100vh) rotate(360deg); opacity: 0; }
}

/* Allowance Cards */
.allowance-card {
    background: linear-gradient(145deg, var(--military-white), #f1f2f6);
    border-radius: 15px;
    box-shadow: 
        0 15px 35px var(--shadow-color),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    margin-bottom: 2rem;
    border: 1px solid rgba(0, 184, 148, 0.1);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
}

.allowance-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(85, 239, 196, 0.1), transparent);
    transition: left 0.6s;
}

.allowance-card:hover::before {
    left: 100%;
}

.allowance-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 
        0 25px 50px var(--shadow-color),
        0 0 30px rgba(0, 184, 148, 0.2);
}

/* Allowance Header */
.allowance-header {
    background: linear-gradient(135deg, var(--military-olive), var(--military-green));
    color: var(--military-white);
    padding: 1.5rem;
    border-radius: 15px 15px 0 0;
    position: relative;
    overflow: hidden;
}

.allowance-header::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.5; }
    50% { transform: scale(1.2); opacity: 0.8; }
}

/* Allowance Icon */
.allowance-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: iconBounce 2s ease-in-out infinite;
    color: var(--military-light-green);
    filter: drop-shadow(0 4px 8px rgba(85, 239, 196, 0.4));
}

@keyframes iconBounce {
    0%, 100% { transform: translateY(0) scale(1); }
    25% { transform: translateY(-5px) scale(1.05); }
    50% { transform: translateY(-10px) scale(1.1); }
    75% { transform: translateY(-5px) scale(1.05); }
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, var(--military-olive), var(--military-green));
    border: none;
    border-radius: 12px;
    padding: 12px 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 20px rgba(108, 92, 231, 0.3);
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(108, 92, 231, 0.4);
}

/* Form Controls */
.form-control, .form-select {
    border-radius: 12px;
    border: 2px solid rgba(108, 92, 231, 0.2);
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus, .form-select:focus {
    border-color: var(--military-green);
    box-shadow: 0 0 0 0.2rem rgba(0, 184, 148, 0.25);
    background: var(--military-white);
}

/* Ministry Logo */
.ministry-logo {
    max-width: 120px;
    height: auto;
    margin-bottom: 1rem;
    filter: drop-shadow(0 4px 12px rgba(45, 52, 54, 0.3));
    object-fit: contain;
    animation: logoGlow 3s ease-in-out infinite;
}

@keyframes logoGlow {
    0%, 100% { filter: drop-shadow(0 4px 12px rgba(45, 52, 54, 0.3)); }
    50% { filter: drop-shadow(0 6px 20px rgba(108, 92, 231, 0.4)); }
}

/* Navbar */
.navbar {
    background: linear-gradient(135deg, var(--military-dark), var(--military-gray)) !important;
    box-shadow: 0 4px 20px var(--shadow-color);
    backdrop-filter: blur(10px);
}

.navbar-brand, .nav-link {
    color: var(--military-white) !important;
    transition: all 0.3s ease;
}

.navbar-brand:hover, .nav-link:hover {
    color: var(--military-light-green) !important;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container {
        max-width: 95%;
    }
    
    .welcome-card {
        padding: 2.5rem 1.5rem;
    }
}

@media (max-width: 992px) {
    .allowance-icon {
        font-size: 2.5rem;
    }
    
    .financial-icon {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .welcome-card {
        padding: 2rem 1rem;
    }
    
    .allowance-header {
        padding: 1rem;
    }
    
    .allowance-icon {
        font-size: 2rem;
    }
    
    .ministry-logo {
        max-width: 80px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: var(--military-light-green);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Coin Animation for Financial Elements */
.coin-flip {
    animation: coinFlip 2s ease-in-out infinite;
    color: var(--military-gold);
}

@keyframes coinFlip {
    0%, 100% { transform: rotateY(0deg); }
    50% { transform: rotateY(180deg); }
}

/* Success Animation */
.success-bounce {
    animation: successBounce 0.6s ease-out;
}

@keyframes successBounce {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

/* Print Styles for Main Page */
@media print {
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    
    body {
        background: white !important;
        font-size: 12px !important;
        line-height: 1.3 !important;
    }
    
    .navbar, .btn, .modal {
        display: none !important;
    }
    
    .ministry-logo {
        max-width: 80px !important;
        margin-bottom: 10px !important;
    }
    
    .welcome-card {
        background: white !important;
        border: 1px solid #ddd !important;
        padding: 15px !important;
        margin-bottom: 15px !important;
        page-break-inside: avoid !important;
    }
    
    .allowance-card {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
        break-inside: avoid !important;
        margin-bottom: 10px !important;
        font-size: 11px !important;
    }
    
    .print-footer {
        position: fixed;
        bottom: 10px;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 8px;
        color: #666;
        border-top: 1px solid #ddd;
        padding-top: 5px;
        display: block !important;
    }
    
    @page {
        size: A4;
        margin: 15mm;
    }
}

/* Delete Button */
.delete-btn {
    position: absolute;
    top: 10px;
    left: 10px;
    background: var(--danger-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: opacity 0.3s ease;
    cursor: pointer;
    z-index: 10;
}

.allowance-card:hover .delete-btn {
    opacity: 1;
}

/* Edit Button */
.edit-btn {
    position: absolute;
    top: 10px;
    left: 50px;
    background: var(--warning-color);
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 1;
    transition: opacity 0.3s ease;
    cursor: pointer;
    z-index: 10;
}

.allowance-card:hover .edit-btn {
    opacity: 1;
}

.allowance-card {
    position: relative;
}

/* Ministry Logo */
.ministry-logo {
    max-width: 120px;
    height: auto;
    margin-bottom: 1rem;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
    object-fit: contain;
}

@media (max-width: 768px) {
    .ministry-logo {
        max-width: 80px;
    }
}



