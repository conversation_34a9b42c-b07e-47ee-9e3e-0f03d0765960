// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    setupEventListeners();
    console.log('Salary difference page loaded');
});

// Setup event listeners
function setupEventListeners() {
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');
    
    if (startDate && endDate) {
        startDate.addEventListener('change', clearResults);
        endDate.addEventListener('change', clearResults);
    }
    
    // Set default dates
    const today = new Date();
    const firstDayOfYear = new Date(today.getFullYear(), 0, 1);
    
    if (startDate) startDate.value = formatDate(firstDayOfYear);
    if (endDate) endDate.value = formatDate(today);
    
    console.log('Event listeners setup complete');
}

// Generate year fields based on date range
function generateYearFields() {
    console.log('Generate year fields function called');
    
    const startDateElement = document.getElementById('startDate');
    const endDateElement = document.getElementById('endDate');
    
    if (!startDateElement || !endDateElement) {
        console.error('Date elements not found');
        alert('خطأ: لم يتم العثور على حقول التاريخ');
        return;
    }
    
    const startDateValue = startDateElement.value;
    const endDateValue = endDateElement.value;
    
    console.log('Start date:', startDateValue);
    console.log('End date:', endDateValue);
    
    if (!startDateValue || !endDateValue) {
        alert('يرجى تحديد تاريخ البداية والنهاية أولاً');
        return;
    }
    
    const startDate = new Date(startDateValue);
    const endDate = new Date(endDateValue);
    
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        alert('يرجى إدخال تواريخ صحيحة');
        return;
    }
    
    if (endDate <= startDate) {
        alert('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
        return;
    }
    
    const years = getYearsBetweenDates(startDate, endDate);
    console.log('Years generated:', years);
    
    const container = document.getElementById('salaryFields');
    const calculateButton = document.getElementById('calculateButtonContainer');
    
    if (!container) {
        console.error('Salary fields container not found');
        alert('خطأ: لم يتم العثور على حاوي حقول الرواتب');
        return;
    }
    
    // Clear existing fields
    container.innerHTML = '';
    
    // Create fields for each year
    years.forEach((year, index) => {
        const fieldDiv = document.createElement('div');
        fieldDiv.className = 'salary-year-field';
        fieldDiv.style.animationDelay = `${index * 0.1}s`;
        
        fieldDiv.innerHTML = `
            <div class="year-label">
                <i class="fas fa-calendar-year"></i>
                راتب سنة ${year}
            </div>
            <input type="number" 
                   class="form-control salary-input" 
                   id="salary_${year}" 
                   placeholder="أدخل الراتب لسنة ${year}"
                   min="0" 
                   step="0.01"
                   required>
        `;
        
        container.appendChild(fieldDiv);
    });
    
    // Show calculate button
    if (calculateButton) {
        calculateButton.style.display = 'block';
    }
    
    // Clear previous results
    clearResults();
    
    console.log(`Created ${years.length} salary fields`);
    
    // Show success message
    showToast(`تم إنشاء ${years.length} حقل للرواتب بنجاح`, 'success');
}

// Get years between two dates
function getYearsBetweenDates(startDate, endDate) {
    const years = [];
    const startYear = startDate.getFullYear();
    const endYear = endDate.getFullYear();
    
    for (let year = startYear; year <= endYear; year++) {
        years.push(year);
    }
    
    return years;
}

// Calculate salary difference
function calculateSalaryDifference() {
    console.log('Calculate salary difference function called');
    
    const startDateElement = document.getElementById('startDate');
    const endDateElement = document.getElementById('endDate');
    
    if (!startDateElement || !endDateElement) {
        alert('خطأ: لم يتم العثور على حقول التاريخ');
        return;
    }
    
    const startDate = new Date(startDateElement.value);
    const endDate = new Date(endDateElement.value);
    const years = getYearsBetweenDates(startDate, endDate);
    
    // Calculate total days in the period
    const totalDays = calculateDaysBetween(startDate, endDate);
    
    // Collect salary data
    const salaryData = [];
    let hasEmptyFields = false;
    
    years.forEach(year => {
        const salaryInput = document.getElementById(`salary_${year}`);
        if (!salaryInput) {
            console.error(`Salary input for year ${year} not found`);
            hasEmptyFields = true;
            return;
        }
        
        const salary = parseFloat(salaryInput.value);
        
        if (!salary || salary <= 0) {
            hasEmptyFields = true;
            salaryInput.classList.add('is-invalid');
        } else {
            salaryInput.classList.remove('is-invalid');
            salaryData.push({
                year: year,
                salary: salary
            });
        }
    });
    
    if (hasEmptyFields) {
        alert('يرجى ملء جميع حقول الرواتب بقيم صحيحة');
        return;
    }
    
    if (salaryData.length === 0) {
        alert('لا توجد بيانات رواتب صحيحة');
        return;
    }
    
    // Calculate differences
    const result = calculateDifferences(salaryData, totalDays);
    console.log('Calculation result:', result);
    
    // Display results
    displayResults(result, startDate, endDate);
    displayPeriodDetails(startDate, endDate, years, totalDays);
    
    showToast('تم حساب فرق الراتب بنجاح', 'success');
}

// Calculate differences between salaries
function calculateDifferences(salaryData, totalDays) {
    const differences = [];
    let totalDifference = 0;
    let totalDifferenceForPeriod = 0;
    
    // Sort salary data by year
    salaryData.sort((a, b) => a.year - b.year);
    
    salaryData.forEach((data, index) => {
        let difference = 0;
        let dailyDifference = 0;
        let periodDifference = 0;
        let isLast = index === salaryData.length - 1;
        
        if (!isLast) {
            // Calculate difference with next year's salary
            const nextYearSalary = salaryData[index + 1].salary;
            difference = nextYearSalary - data.salary;
            dailyDifference = difference / 30;
            
            // Calculate days for this specific year
            const yearDays = 360; // Full year = 360 days in our system
            periodDifference = dailyDifference * yearDays;
            
            totalDifference += difference;
            totalDifferenceForPeriod += periodDifference;
        }
        
        differences.push({
            year: data.year,
            salary: data.salary,
            nextYearSalary: !isLast ? salaryData[index + 1].salary : null,
            difference: difference,
            dailyDifference: dailyDifference,
            periodDifference: periodDifference,
            yearDays: !isLast ? 360 : 0,
            isLast: isLast
        });
    });
    
    return {
        differences: differences,
        totalDifference: totalDifference,
        totalDifferenceForPeriod: totalDifferenceForPeriod,
        yearCount: salaryData.length,
        totalDays: totalDays
    };
}

// Display calculation results
function displayResults(result, startDate, endDate) {
    const resultsCard = document.getElementById('resultsCard');
    const resultsContent = document.getElementById('resultsContent');
    
    if (!resultsCard || !resultsContent) {
        console.error('Results elements not found');
        return;
    }
    
    let html = '';
    
    // Individual year differences
    html += '<h6 class="mb-3">فرق الراتب بين السنوات المتتالية:</h6>';
    
    result.differences.forEach(diff => {
        if (!diff.isLast) {
            const diffClass = diff.difference > 0 ? 'difference-positive' : 
                             diff.difference < 0 ? 'difference-negative' : 'difference-zero';
            
            const diffIcon = diff.difference > 0 ? 'fa-arrow-up text-success' : 
                            diff.difference < 0 ? 'fa-arrow-down text-danger' : 'fa-minus text-muted';
            
            const diffText = diff.difference === 0 ? 'لا يوجد فرق' :
                            `فرق شهري: ${formatCurrency(Math.abs(diff.difference))}`;
            
            const periodDiffText = diff.difference === 0 ? '' :
                                  `فرق السنة: ${formatCurrency(Math.abs(diff.periodDifference))}`;
            
            html += `
                <div class="difference-item ${diffClass}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>من سنة ${diff.year} إلى سنة ${diff.year + 1}</strong>
                            <div class="text-muted">راتب ${diff.year}: ${formatCurrency(diff.salary)}</div>
                            <div class="text-muted">راتب ${diff.year + 1}: ${formatCurrency(diff.nextYearSalary)}</div>
                            ${diff.difference !== 0 ? `
                            <div class="text-muted small">فرق يومي: ${formatCurrency(Math.abs(diff.dailyDifference))}</div>
                            <div class="text-muted small">عدد أيام السنة: ${diff.yearDays} يوم</div>
                            ` : ''}
                        </div>
                        <div class="text-end">
                            <i class="fas ${diffIcon} me-2"></i>
                            <div class="fw-bold">${diffText}</div>
                            ${periodDiffText ? `<div class="small text-primary">${periodDiffText}</div>` : ''}
                        </div>
                    </div>
                    ${diff.difference !== 0 ? `
                    <div class="calculation-formula mt-2">
                        المعادلة: (${formatCurrency(diff.nextYearSalary)} - ${formatCurrency(diff.salary)}) ÷ 30 × ${diff.yearDays} = ${formatCurrency(diff.periodDifference)}
                    </div>
                    ` : ''}
                </div>
            `;
        } else {
            // Last year - no calculation
            html += `
                <div class="difference-item difference-neutral">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>سنة ${diff.year}</strong>
                            <div class="text-muted">راتب ${diff.year}: ${formatCurrency(diff.salary)}</div>
                        </div>
                        <div class="text-end">
                            <i class="fas fa-info-circle text-info me-2"></i>
                            <div class="fw-bold">السنة الأخيرة</div>
                            <div class="small text-muted">لا يوجد حساب</div>
                        </div>
                    </div>
                </div>
            `;
        }
    });
    
    // Summary
    html += `
        <div class="difference-item">
            <div class="d-flex justify-content-between">
                <span>عدد السنوات</span>
                <span><strong>${result.yearCount} سنة</strong></span>
            </div>
        </div>
        <div class="difference-item">
            <div class="d-flex justify-content-between">
                <span>عدد الفروقات المحسوبة</span>
                <span><strong>${result.yearCount - 1} فرق</strong></span>
            </div>
        </div>
        <div class="difference-item">
            <div class="d-flex justify-content-between">
                <span>إجمالي الفترة المحددة</span>
                <span><strong>${result.totalDays} يوم</strong></span>
            </div>
        </div>
    `;
    
    // Monthly total difference
    const monthlyDiffClass = result.totalDifference > 0 ? 'text-success' : 
                            result.totalDifference < 0 ? 'text-danger' : 'text-muted';
    
    html += `
        <div class="difference-item">
            <div class="d-flex justify-content-between">
                <span>إجمالي الفروقات الشهرية</span>
                <span class="${monthlyDiffClass}"><strong>${formatCurrency(result.totalDifference)}</strong></span>
            </div>
        </div>
    `;
    
    // Period total difference (مجموع فروقات كل سنة)
    const periodDiffClass = result.totalDifferenceForPeriod > 0 ? 'text-success' : 
                           result.totalDifferenceForPeriod < 0 ? 'text-danger' : 'text-muted';
    
    html += `
        <div class="difference-item total-difference">
            <div class="d-flex justify-content-between">
                <span><strong>إجمالي فروقات الرواتب</strong></span>
                <span class="${periodDiffClass}"><strong>${formatCurrency(result.totalDifferenceForPeriod)}</strong></span>
            </div>
            <div class="small text-muted mt-1">
                مجموع فروقات كل سنة (360 يوم لكل سنة)
            </div>
        </div>
    `;
    
    resultsContent.innerHTML = html;
    resultsCard.style.display = 'block';
    
    // Add success animation
    resultsCard.classList.add('success-bounce');
    setTimeout(() => {
        resultsCard.classList.remove('success-bounce');
    }, 600);
}

// Display period details
function displayPeriodDetails(startDate, endDate, years, totalDays) {
    const detailsCard = document.getElementById('periodDetails');
    const detailsContent = document.getElementById('periodDetailsContent');
    
    if (!detailsCard || !detailsContent) {
        console.error('Period details elements not found');
        return;
    }
    
    const html = `
        <div class="period-item">
            <span class="period-label">تاريخ البداية</span>
            <span class="period-value">${formatDateArabic(startDate)}</span>
        </div>
        <div class="period-item">
            <span class="period-label">تاريخ النهاية</span>
            <span class="period-value">${formatDateArabic(endDate)}</span>
        </div>
        <div class="period-item">
            <span class="period-label">إجمالي الأيام</span>
            <span class="period-value">${totalDays} يوم</span>
        </div>
        <div class="period-item">
            <span class="period-label">عدد السنوات</span>
            <span class="period-value">${years.length} سنة</span>
        </div>
        <div class="period-item">
            <span class="period-label">السنوات المشمولة</span>
            <span class="period-value">${years.join(', ')}</span>
        </div>
        <div class="period-item calculation-note">
            <span class="period-label">طريقة الحساب</span>
            <span class="period-value">30 يوم لكل شهر</span>
        </div>
    `;
    
    detailsContent.innerHTML = html;
    detailsCard.style.display = 'block';
}

// Clear results
function clearResults() {
    const resultsCard = document.getElementById('resultsCard');
    const periodDetails = document.getElementById('periodDetails');
    
    if (resultsCard) resultsCard.style.display = 'none';
    if (periodDetails) periodDetails.style.display = 'none';
}

// Utility functions
function formatDate(date) {
    return date.toISOString().split('T')[0];
}

function formatDateArabic(date) {
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR',
        minimumFractionDigits: 2
    }).format(amount);
}

// Show toast notification
function showToast(message, type = 'info') {
    // Create toast element
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-info-circle'} me-2"></i>
            ${message}
        </div>
    `;
    
    // Add to body
    document.body.appendChild(toast);
    
    // Show toast
    setTimeout(() => {
        toast.classList.add('show');
    }, 100);
    
    // Remove toast after 3 seconds
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// Export to PDF
function exportToPDF() {
    window.print();
}

// Test function to verify script loading
function testFunction() {
    console.log('Salary difference script loaded successfully');
    alert('السكريبت يعمل بشكل صحيح');
}

// Calculate days between two dates (30 days per month system)
function calculateDaysBetween(start, end) {
    const startYear = start.getFullYear();
    const startMonth = start.getMonth() + 1;
    const startDay = start.getDate();
    
    const endYear = end.getFullYear();
    const endMonth = end.getMonth() + 1;
    const endDay = end.getDate();
    
    const adjustedStartDay = Math.min(startDay, 30);
    const adjustedEndDay = Math.min(endDay, 30);
    
    const totalStartMonths = (startYear * 12) + startMonth;
    const totalEndMonths = (endYear * 12) + endMonth;
    
    let totalDays = 0;
    
    if (totalStartMonths === totalEndMonths) {
        totalDays = adjustedEndDay - adjustedStartDay + 1;
    } else {
        totalDays += (30 - adjustedStartDay + 1);
        const monthsBetween = totalEndMonths - totalStartMonths - 1;
        totalDays += monthsBetween * 30;
        totalDays += adjustedEndDay;
    }
    
    return Math.max(0, totalDays);
}




