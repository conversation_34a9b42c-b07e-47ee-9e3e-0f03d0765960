/* Allowance Page Specific Styles */

.allowance-header-card {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.allowance-icon-large {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.calculation-card, .results-card, .period-details-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 1rem 1.5rem;
}

/* Results Styling - Fixed Colors */
.result-item {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 1rem;
    border-right: 4px solid var(--primary-color);
}

.result-label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.result-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2c3e50;
}

.result-details {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.5rem;
}

.total-result {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    border: none;
    text-align: center;
    padding: 1.5rem;
    border-radius: 10px;
    margin-top: 1rem;
}

.total-result .total-label {
    color: white;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.total-result .total-value {
    color: white;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.total-result .total-details {
    color: rgba(255,255,255,0.9);
    font-size: 0.9rem;
}

/* Period Details */
.period-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e9ecef;
}

.period-item:last-child {
    border-bottom: none;
}

.period-label {
    font-weight: 500;
    color: #495057;
}

.period-value {
    font-weight: 600;
    color: var(--primary-color);
}

/* Animation for results */
.result-item {
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Print Styles */
@media print {
    * {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    body {
        font-size: 12px !important;
        line-height: 1.3 !important;
        margin: 0 !important;
        padding: 10px !important;
    }
    
    .navbar, .btn, .card-footer, .calculation-card {
        display: none !important;
    }
    
    .container {
        max-width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .col-lg-6 {
        width: 100% !important;
        max-width: 100% !important;
    }
    
    /* Print Header */
    .allowance-header-card {
        background: linear-gradient(135deg, #2c3e50, #3498db) !important;
        color: white !important;
        padding: 15px !important;
        margin-bottom: 15px !important;
        border-radius: 10px !important;
        text-align: center !important;
        page-break-inside: avoid !important;
    }
    
    .allowance-header-card h2 {
        font-size: 18px !important;
        margin: 5px 0 !important;
    }
    
    .allowance-header-card .lead {
        font-size: 12px !important;
        margin: 5px 0 !important;
    }
    
    .allowance-icon-large {
        font-size: 24px !important;
        margin: 5px 0 !important;
    }
    
    /* Print Results */
    .results-card, .period-details-card {
        background: white !important;
        border: 1px solid #ddd !important;
        border-radius: 8px !important;
        margin-bottom: 10px !important;
        box-shadow: none !important;
        page-break-inside: avoid !important;
    }
    
    .card-header {
        background: #f8f9fa !important;
        padding: 8px 12px !important;
        font-size: 14px !important;
        font-weight: 600 !important;
        border-bottom: 1px solid #dee2e6 !important;
    }
    
    .card-body {
        padding: 10px !important;
    }
    
    .result-item, .period-item {
        padding: 5px 0 !important;
        font-size: 11px !important;
        border-bottom: 1px solid #eee !important;
    }
    
    .result-label, .period-label {
        font-weight: 500 !important;
        font-size: 11px !important;
    }
    
    .result-value, .period-value {
        font-weight: 600 !important;
        font-size: 11px !important;
        color: #2c3e50 !important;
    }
    
    .total-result {
        background: linear-gradient(135deg, #27ae60, #2ecc71) !important;
        color: #ffffff !important;
        margin: 8px -10px -10px !important;
        padding: 10px !important;
        border-radius: 0 0 8px 8px !important;
    }
    
    .total-result .result-label,
    .total-result .result-value {
        color: white !important;
        font-size: 14px !important;
        font-weight: 700 !important;
    }
    
    /* Print Footer */
    .print-footer {
        position: fixed;
        bottom: 10px;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 8px;
        color: #666;
        border-top: 1px solid #ddd;
        padding-top: 5px;
    }
    
    /* Ensure single page */
    @page {
        size: A4;
        margin: 15mm;
    }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .allowance-header-card {
        padding: 1.5rem;
    }
    
    .allowance-icon-large {
        font-size: 3rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .result-value {
        font-size: 1.1rem;
    }
    
    .total-result .result-value {
        font-size: 1.5rem;
    }
}

/* Loading State */
.calculating {
    opacity: 0.7;
    pointer-events: none;
}

.calculating .btn {
    position: relative;
}

.calculating .btn::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    margin: auto;
    border: 2px solid transparent;
    border-top-color: #ffffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}

/* Info Alert Styling */
.alert-info {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border: 1px solid #2196f3;
    border-radius: 10px;
    color: #1565c0;
    font-size: 0.9rem;
}

.alert-info i {
    color: #2196f3;
}

/* Calculation Breakdown Styles */
.calculation-breakdown {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.calculation-breakdown h6 {
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #dee2e6;
}

.calculation-details {
    margin-top: 10px;
}

.calculation-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #f1f3f4;
}

.calculation-item:last-child {
    border-bottom: none;
}

.calculation-label {
    font-weight: 500;
    color: #495057;
}

.calculation-value {
    font-weight: 600;
    color: var(--primary-color);
}

.total-item {
    background: #e8f5e8;
    padding: 8px 10px;
    border-radius: 5px;
    margin-top: 8px;
    border: 1px solid #28a745;
}

.total-item .calculation-label,
.total-item .calculation-value {
    color: #155724;
}

.calculation-formula {
    background: #fff;
    padding: 8px;
    border-radius: 4px;
    margin: 8px 0;
    border: 1px solid #e9ecef;
}

.calculation-formula small {
    font-size: 11px;
    line-height: 1.3;
}

/* Current Salary Based Alert */
.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .calculation-breakdown {
        padding: 10px;
    }
    
    .calculation-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 3px;
    }
    
    .calculation-formula {
        padding: 6px;
    }
    
    .calculation-formula small {
        font-size: 10px;
    }
}

/* Yearly Salary Fields */
.yearly-salary-field {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    animation: slideInUp 0.5s ease-out;
    transition: all 0.3s ease;
}

.yearly-salary-field:hover {
    background: #e9ecef;
    border-color: var(--primary-color);
}

.yearly-salary-field label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
}

.yearly-salary-field label i {
    color: var(--primary-color);
}

.yearly-salary-input {
    border: 2px solid #dee2e6;
    border-radius: 6px;
    padding: 10px 12px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.yearly-salary-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(52, 144, 220, 0.25);
}

.yearly-salary-input:valid {
    border-color: #28a745;
}

.yearly-salary-input:invalid {
    border-color: #dc3545;
}

/* Container styling */
#yearlySalariesContainer {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

#yearlySalariesContainer .alert {
    margin-bottom: 15px;
    border-radius: 6px;
}

/* Animation for yearly fields */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .yearly-salary-field {
        padding: 12px;
        margin-bottom: 8px;
    }
    
    .yearly-salary-input {
        padding: 8px 10px;
        font-size: 13px;
    }
    
    #yearlySalariesContainer {
        padding: 15px;
    }
}

/* Loading state for dynamic fields */
.yearly-salary-field.loading {
    opacity: 0.7;
    pointer-events: none;
}

.yearly-salary-field.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Professional Print Report Styles */
.professional-report {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: 11px;
    line-height: 1.3;
    color: #000;
    max-width: 100%;
    margin: 0;
    padding: 0;
}

.report-header {
    text-align: center;
    border-bottom: 2px solid #2c5530;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.ministry-logo-text {
    font-size: 14px;
    font-weight: bold;
    color: #2c5530;
    margin-bottom: 3px;
}

.department-title {
    font-size: 12px;
    font-weight: 600;
    color: #444;
    margin-bottom: 3px;
}

.report-title {
    font-size: 16px;
    font-weight: bold;
    color: #2c5530;
    margin: 8px 0 5px 0;
}

.report-date {
    font-size: 10px;
    color: #666;
}

.report-content {
    margin-bottom: 20px;
}

.employee-info, .calculation-details, .results-summary {
    margin-bottom: 12px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.employee-info h6, .calculation-details h6, .results-summary h6 {
    font-size: 12px;
    font-weight: bold;
    color: #2c5530;
    margin-bottom: 6px;
    border-bottom: 1px solid #eee;
    padding-bottom: 3px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    padding: 2px 0;
    font-size: 10px;
}

.info-label {
    font-weight: 600;
    color: #444;
}

.info-value {
    font-weight: 500;
    color: #000;
}

.report-footer {
    margin-top: 15px;
    border-top: 1px solid #ddd;
    padding-top: 10px;
}

.signature-section {
    display: flex;
    justify-content: space-around;
    margin-bottom: 10px;
}

.signature-box {
    text-align: center;
    font-size: 9px;
}

.signature-line {
    width: 120px;
    height: 1px;
    border-bottom: 1px solid #000;
    margin: 15px auto 5px auto;
}

.system-info {
    text-align: center;
    font-size: 8px;
    color: #666;
}

/* Enhanced Print Styles */
@media print {
    * {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
    
    body {
        font-size: 12px !important;
        line-height: 1.4 !important;
        margin: 0 !important;
        padding: 10px !important;
        background: white !important;
        color: #000 !important;
    }
    
    .navbar, .btn, .card-footer, .calculation-card {
        display: none !important;
    }
    
    .container {
        max-width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }
    
    /* Print Results Card */
    .results-card {
        background: white !important;
        border: 2px solid #000 !important;
        border-radius: 8px !important;
        margin-bottom: 15px !important;
        box-shadow: none !important;
        page-break-inside: avoid !important;
        display: block !important;
    }
    
    .card-header {
        background: #f8f9fa !important;
        color: #000 !important;
        padding: 10px 15px !important;
        font-size: 14px !important;
        font-weight: 700 !important;
        border-bottom: 2px solid #000 !important;
    }
    
    .card-body {
        padding: 15px !important;
        background: white !important;
    }
    
    /* Print Result Items */
    .result-item {
        background: #f8f9fa !important;
        border: 1px solid #000 !important;
        border-right: 4px solid #000 !important;
        padding: 10px !important;
        margin-bottom: 10px !important;
        border-radius: 5px !important;
    }
    
    .result-label {
        color: #000 !important;
        font-weight: 600 !important;
        font-size: 12px !important;
        display: block !important;
    }
    
    .result-value {
        color: #000 !important;
        font-weight: 700 !important;
        font-size: 14px !important;
        display: block !important;
        margin-top: 5px !important;
    }
    
    .result-details {
        color: #333 !important;
        font-size: 10px !important;
        margin-top: 5px !important;
        display: block !important;
    }
    
    /* Print Total Result */
    .total-result {
        background: #e8f5e8 !important;
        border: 2px solid #000 !important;
        color: #000 !important;
        padding: 15px !important;
        text-align: center !important;
        border-radius: 8px !important;
        margin-top: 15px !important;
    }
    
    .total-result .total-label {
        color: #000 !important;
        font-size: 14px !important;
        font-weight: 700 !important;
        display: block !important;
        margin-bottom: 8px !important;
    }
    
    .total-result .total-value {
        color: #000 !important;
        font-size: 18px !important;
        font-weight: 700 !important;
        display: block !important;
        margin-bottom: 8px !important;
    }
    
    .total-result .total-details {
        color: #000 !important;
        font-size: 12px !important;
        display: block !important;
    }
    
    /* Period Details Print */
    .period-details-card {
        background: white !important;
        border: 2px solid #000 !important;
        border-radius: 8px !important;
        margin-bottom: 15px !important;
        display: block !important;
    }
    
    .period-item {
        display: flex !important;
        justify-content: space-between !important;
        padding: 8px 0 !important;
        border-bottom: 1px solid #ccc !important;
        color: #000 !important;
    }
    
    .period-label, .period-value {
        color: #000 !important;
        font-size: 11px !important;
        font-weight: 600 !important;
    }
}

